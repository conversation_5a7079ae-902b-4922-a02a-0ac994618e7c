<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="会员id" prop="pkUserMain">
        <el-input
          v-model="queryParams.pkUserMain"
          placeholder="请输入会员id"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入昵称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="会员等级" prop="levelIds">
        <el-select
          v-model="queryParams.levelIds"
          placeholder="请选择会员等级"
          clearable
          multiple
          collapse-tags
          collapse-tags-tooltip
          class="!w-240px"
        >
          <el-option
            v-for="item in levelOptions"
            :key="item.id"
            :label="item.levelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:user:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:user:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" fit @sort-change="handleSortChange">
      <el-table-column label="id" align="center" prop="pkUserMain" sortable="custom">
        <template #default="scope">
          <el-button 
            link
            type="primary"
            @click="router.push({path: '/resource-manage/worksmain', query: {pkUserMain: scope.row.pkUserMain}})">
            {{ scope.row.pkUserMain }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="会员昵称" align="center" prop="phone" width="150px">
      <template #default="scope">
          <el-button 
            link
            type="primary"
            @click="router.push({path: '/resource-manage/worksmain', query: {pkUserMain: scope.row.pkUserMain}})">
            {{ scope.row.nickname }}
          </el-button>
     
          <div class="flex items-center justify-center">
            <span>{{ scope.row.phone }}</span>
            <el-button 
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.phone)">
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column label="全景" align="center" prop="tourCount" />
      <el-table-column label="子账号" align="center" prop="accountCount" />
      <el-table-column label="扩容" align="center" key="spaceSum">
      <template #default="scope">
            <span>{{ scope.row.spaceSum != null ? convertStorageSize(scope.row.spaceSum * 1024) : '0KB' }}</span>
        </template>
      </el-table-column>   
      <el-table-column label="空间占用" align="center" key="spaceSize" width="120px">
        <template #default="scope">
            <span>{{ convertStorageSize(scope.row.spaceSize * 1024) }}</span>
        </template>
      </el-table-column>       
      <el-table-column label="用户等级" align="center" prop="levelName" sortable="custom"/>
      <el-table-column label="过期时间" align="center" prop="expire" width="180px" sortable="custom">
        <template #default="scope">
          <span>{{ formatExpireTime(scope.row.expire) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="注册时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
        sortable="custom"
      />
      <el-table-column
        label="最近登录"
        align="center"
        prop="lastTime"
        :formatter="dateFormatter"
        width="180px"
        sortable="custom"
      />
      <el-table-column label="余额" align="center" prop="amount" sortable="custom" />
      <el-table-column label="封号" key="state" >
        <template #default="scope">
              <el-switch
                v-model="scope.row.state"
                :active-value="1"
                :inactive-value="0"
                @change="handleStateChange(scope.row)"
              />
            </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">

          <div class="flex items-center justify-center">
            <el-dropdown
                @command="(command) => handleCommand(command, scope.row)"
                v-hasPermi="[
                  'vr:user:update',
                  'vr:user:update-password',
                  'vr:user:update-space',
                  'vr:user:bind-code',
                  'vr:user:transfer-dealer',
                  'vr:user:delete'
                ]"
              >
                <el-button type="primary" link><Icon icon="ep:d-arrow-right" /> 编辑</el-button>
                <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="handleUpdate"
                    v-if="checkPermi(['vr:user:update'])"
                  >
                    <Icon icon="ep:edit" />基本信息
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleUpdateLevel"
                    v-if="checkPermi(['vr:user:update-level'])"
                  >
                    <Icon icon="ep:key" />变更等级
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleUpdateSpace"
                    v-if="checkPermi(['vr:user:update-space'])"
                  >
                    <Icon icon="ep:circle-check" />添加容量
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleMachineCode"
                    v-if="checkPermi(['vr:user:bind-code'])"
                  >
                    <Icon icon="ep:key" />机器码
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleTransferDealer"
                    v-if="checkPermi(['vr:user:transfer-dealer'])"
                  >
                    <Icon icon="ep:position" />数据转移
                  </el-dropdown-item>
                  <el-dropdown-item
                    command="handleDelete"
                    v-if="checkPermi(['vr:user:delete'])"
                  >
                    <Icon icon="ep:delete" />删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            <el-button
            link
            type="primary"
            @click="handleCommand('spaceList', scope.row)">
            扩容列表
          </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <UserForm ref="formRef" @success="getList" />
  <UserLevelForm ref="userLevelFormRef" @success="getList" />
  <UserSpaceForm ref="userSpaceFormRef" @success="getList" />
  <MachineCodeForm ref="machineCodeFormRef" @success="getList" />
  <!-- 添加新的扩容记录列表弹窗组件 -->
  <UserSpaceListDialog ref="spaceListDialogRef" />
  <!-- 数据转移弹窗 -->
  <TransferDealerForm ref="transferDealerFormRef" @success="getList" />
</template>

<script setup lang="ts">
import { checkPermi } from '@/utils/permission'
import { convertStorageSize } from '@/utils/spaceUtils'
import { CommonStatusEnum } from '@/utils/constants'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { UserApi, UserVO, UserInfoVO } from '@/api/vr/user'
import UserForm from './UserForm.vue'
import UserLevelForm from './UserLevelForm.vue'
import UserSpaceForm from './UserSpaceForm.vue'
import MachineCodeForm from './MachineCodeForm.vue'
import UserSpaceListDialog from './UserSpaceListDialog.vue'
import TransferDealerForm from './TransferDealerForm.vue'
import { useUserStore } from '@/store/modules/user'
import { UserLevelApi } from '@/api/vr/userLevel'
/** 用户 列表 */
defineOptions({ name: 'User' })

const router = useRouter()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<UserVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pkUserMain: undefined,
  phone: undefined,
  nickname: undefined,
  tenantId: undefined,
  levelIds: [],
  sortField: undefined,
  sortOrder: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const userStore = useUserStore()

// 添加以下数据定义
const levelOptions = ref([]) // 会员等级选项

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await UserApi.getUserPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.sortField = undefined
  queryParams.sortOrder = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 添加/修改空间 */
const userSpaceFormRef = ref()
const openSpaceForm = (id?: number) => {
  userSpaceFormRef.value.open(id)
}

/** 机器码 */
const machineCodeFormRef = ref()
const openMachineCodeForm = (id?: number) => {
  machineCodeFormRef.value.open(id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await UserApi.deleteUser(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 变更等级 */
const userLevelFormRef = ref()
const handleUpdateLevel = (id?: number) => {
  userLevelFormRef.value.open(id)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await UserApi.exportUser(queryParams)
    download.excel(data, '用户.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 打开数据转移弹窗 */
const transferDealerFormRef = ref()
const openTransferDealerForm = (id?: number) => {
  transferDealerFormRef.value.open(id)
}

/** 操作分发 */
const handleCommand = (command: string, row: UserInfoVO) => {
  switch (command) {
    case 'handleUpdate':
      openForm('update', row.pkUserMain)
      break
    case 'handleUpdateLevel':
      handleUpdateLevel(row.pkUserMain)
      break
    case 'handleUpdateSpace':
      openSpaceForm(row.pkUserMain)
      break
    case 'handleMachineCode':
      openMachineCodeForm(row.pkUserMain)
      break
    case 'handleTransferDealer':
      openTransferDealerForm(row.pkUserMain)
      break
    case 'handleDelete':
      handleDelete(row.pkUserMain)
      break
    case 'spaceList':
      spaceListDialogRef.value.open(row.pkUserMain)
      break
    default:
      break
  }
}

/** 封号 */
const handleStateChange = async (row: UserInfoVO) => {
  try {
    // 修改状态的二次确认
    //const text = row.state === CommonStatusEnum.ENABLE ? '停用' : '启用'
    // await message.confirm('确认要"' + text + '""' + row.nickname + '"用户吗?')
    // 发起修改状态
    await UserApi.updateUserState(row.pkUserMain, row.state)
    // 刷新列表
    await getList()
  } catch {
    // 取消后，进行恢复按钮
    row.state =
      row.state === CommonStatusEnum.ENABLE ? CommonStatusEnum.DISABLE : CommonStatusEnum.ENABLE
  }
}

function formatExpireTime(expire: number) {
    if (expire == 0) {
        return '--';
      }

      // 将 expire 转换为毫秒时间戳
      const expireTime = expire * 1000;

      // 使用 Date 对象进行格式化
      const date = new Date(expireTime);

      // 格式化为 YYYY-MM-DD HH:MM:SS
      const formattedDate = formatDate(date);
      return formattedDate;
  }

// 自定义的日期格式化函数
function formatDate(date: any) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');  // 月份从 0 开始，所以要加 1
  const day = String(date.getDate()).padStart(2, '0');
  const hour = String(date.getHours()).padStart(2, '0');
  const minute = String(date.getMinutes()).padStart(2, '0');
  const second = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
}

const clipboard = (text: string | number) => {
  navigator.clipboard.writeText(String(text))
  message.success('复制成功')
}

/** 获取会员等级列表 */
const getLevelOptions = async () => {
  try {
    // 假设这是获取会员等级列表的 API
    const data = await UserLevelApi.getUserLevelPage({})
    levelOptions.value = data.list
  } catch (error) {
    console.error('获取会员等级列表失败:', error)
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
  getLevelOptions() // 添加获取会员等级列表的调用
})

/** 添加表格排序事件处理 */
const handleSortChange = ({ prop, order }) => {
  console.log(prop, order)
  queryParams.sortField = prop
  queryParams.sortOrder = order === 'ascending' ? 'asc' : 'desc'
  getList()
}

// 添加扩容记录列表弹窗的引用
const spaceListDialogRef = ref()
</script>