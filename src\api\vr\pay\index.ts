import request from '@/config/axios'


export interface UserInfoVO {
  pkUserMain: number // 主鍵
  phone: string // 手机号，登录账号
  nickname: string // 昵称
  password: string // 密码
  limitSon: number // 子账号数量，为0就按分组
  state: number
  level: number
  expire: Date[]
}

// 用户 VO
export interface WorkingCapitalVO {
  userAmount: number // 用户余额
  redpackAmount: number // 红包金额
  rewardAmount: number // 打赏金额
}


// 资金 API
export const WorkingCapitalApi = {
  // 获取资金信息
  getWorkingCapital: async () => {
    return await request.get({ url: '/vr/workingCapital/getWorkingCapital' })
  }
}

export const IosPayApi = {
  // 获取ios是否可以支付
  getIosPay: async () => {
    return await request.get({ url: '/vr/payConfig/getIosPaySwitch' })
  },
  // 设置ios是否可以支付
  setIosPay: async (status: boolean) => {
    return await request.get({ 
      url: '/vr/payConfig/setIosPaySwitch',
      params: { status }
    })
  }
}





