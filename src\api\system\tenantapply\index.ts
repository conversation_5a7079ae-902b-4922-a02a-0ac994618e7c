import request from '@/config/axios'

// 经销商申请 VO
export interface TenantApplyVO {
  id: number // 租户编号
  name: string // 姓名
  mobile: string // 手机号
  status: number // 状态
  msg: string // 留言
}

// 经销商申请 API
export const TenantApplyApi = {
  // 查询经销商申请分页
  getTenantApplyPage: async (params: any) => {
    return await request.get({ url: `/system/tenant-apply/page`, params })
  },

  // 查询经销商申请详情
  getTenantApply: async (id: number) => {
    return await request.get({ url: `/system/tenant-apply/get?id=` + id })
  },

  // 新增经销商申请
  createTenantApply: async (data: TenantApplyVO) => {
    return await request.post({ url: `/system/tenant-apply/create`, data })
  },

  // 修改经销商申请
  updateTenantApply: async (data: TenantApplyVO) => {
    return await request.put({ url: `/system/tenant-apply/update`, data })
  },

  // 删除经销商申请
  deleteTenantApply: async (id: number) => {
    return await request.delete({ url: `/system/tenant-apply/delete?id=` + id })
  },

  // 导出经销商申请 Excel
  exportTenantApply: async (params) => {
    return await request.download({ url: `/system/tenant-apply/export-excel`, params })
  },

  // 修改经销商申请状态
  updateTenantApplyState: async (id: number, status: number) => {
    const data = {
      id,
      status
    }
    return request.put({ url: '/system/tenant-apply/updateState', data: data })
  },
}

