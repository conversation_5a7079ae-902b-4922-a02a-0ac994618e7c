import request from '@/config/axios'

// 文章分类 VO
export interface ArticleCatVO {
  id: number // id
  catName: string // 分类名称
  sort: number // 排序
}

// 文章分类 API
export const ArticleCatApi = {
  // 查询文章分类分页
  getArticleCatPage: async (params: any) => {
    return await request.get({ url: `/vr/article-cat/page`, params })
  },

  // 查询文章分类详情
  getArticleCat: async (id: number) => {
    return await request.get({ url: `/vr/article-cat/get?id=` + id })
  },

  // 新增文章分类
  createArticleCat: async (data: ArticleCatVO) => {
    return await request.post({ url: `/vr/article-cat/create`, data })
  },

  // 修改文章分类
  updateArticleCat: async (data: ArticleCatVO) => {
    return await request.put({ url: `/vr/article-cat/update`, data })
  },

  // 删除文章分类
  deleteArticleCat: async (id: number) => {
    return await request.delete({ url: `/vr/article-cat/delete?id=` + id })
  },

  // 导出文章分类 Excel
  exportArticleCat: async (params) => {
    return await request.download({ url: `/vr/article-cat/export-excel`, params })
  },
}
