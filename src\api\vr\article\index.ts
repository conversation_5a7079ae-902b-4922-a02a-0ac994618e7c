import request from '@/config/axios'

// 文章详情 VO
export interface ArticleVO {
  id: number // id
  title: string // 文章标题
  catId: number // 分类ID
  keywords: string // 关键词
  content: string // 正文
  sort: number // 排序规则，越小越靠前
  thumb: string // 封面
}

// 文章分类 VO
export interface ArticleCatVO {
  id: number // id
  catName: string // 分类名称
}

// 文章详情 API
export const ArticleApi = {
  // 查询文章详情分页
  getArticlePage: async (params: any) => {
    return await request.get({ url: `/vr/article/page`, params })
  },

  // 查询文章详情详情
  getArticle: async (id: number) => {
    return await request.get({ url: `/vr/article/get?id=` + id })
  },

  // 新增文章详情
  createArticle: async (data: ArticleVO) => {
    return await request.post({ url: `/vr/article/create`, data })
  },

  // 修改文章详情
  updateArticle: async (data: ArticleVO) => {
    return await request.put({ url: `/vr/article/update`, data })
  },

  // 删除文章详情
  deleteArticle: async (id: number) => {
    return await request.delete({ url: `/vr/article/delete?id=` + id })
  },

  // 导出文章详情 Excel
  exportArticle: async (params) => {
    return await request.download({ url: `/vr/article/export-excel`, params })
  },

  // 获取文章分类
  getArticleCatList: async () => {
    return await request.get({ url: `/vr/article-cat/list` })
  },
}
