<template>

  <!-- 使用 flex 布局的容器 -->
  <div class="flex gap-4">
    <!-- 左侧分组列表 -->
    <ContentWrap class="w-1/3">
      <!-- 分组操作按钮 -->
      <div class="mb-4">
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:help-group:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增分组
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="list"
        :stripe="true"
        :show-overflow-tooltip="true"
        highlight-current-row
        @current-change="handleCurrentChange"
      >
        <!-- <el-table-column label="主键" align="center" prop="id" /> -->
        <el-table-column label="分组" align="left" prop="title" min-width="200" />
        <el-table-column label="排序" align="center" prop="sort" width="80" />
        <!-- <el-table-column label="创建时间" align="center" prop="time" /> -->
        <el-table-column label="操作" align="center" width="120">
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['vr:help-group:update']"
            >
              编辑
            </el-button>
            <el-button
              link
              type="danger"
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['vr:help-group:delete']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </ContentWrap>

    <!-- 右侧问题列表 -->
    <ContentWrap class="w-2/3">
      <HelpList :gid="currentRow.id" />
    </ContentWrap>
  </div>

  <!-- 表单弹窗：添加/修改 -->
  <HelpGroupForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { HelpGroupApi, HelpGroupVO } from '@/api/vr/help'
import HelpGroupForm from './HelpGroupForm.vue'
import HelpList from './components/HelpList.vue'

/** 问题分组 列表 */
defineOptions({ name: 'HelpGroup' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<HelpGroupVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await HelpGroupApi.getHelpGroupPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await HelpGroupApi.deleteHelpGroup(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await HelpGroupApi.exportHelpGroup(queryParams)
    download.excel(data, '问题分组.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 选中行操作 */
const currentRow = ref({}) // 选中行
const handleCurrentChange = (row) => {
  currentRow.value = row
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>