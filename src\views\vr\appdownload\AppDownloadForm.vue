<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" v-loading="formLoading">
      <el-form-item label="标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="发布时间" prop="releaseTime">
        <el-date-picker
          v-model="formData.releaseTime"
          type="date"
          value-format="x"
          placeholder="选择发布时间"
        />
      </el-form-item>
      <el-form-item label="发布说明" prop="entry">
        <el-input v-model="formData.entry" placeholder="请输入发布说明" type="textarea" :rows="6" />
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.VR_APP_DOWNLOAD_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.type === 2" label="AppStore地址" prop="url">
        <el-input v-model="formData.url" placeholder="https://apps.apple.com/app/ijoyer/id1609199973" />
      </el-form-item>
      <el-form-item v-if="formData.type != undefined && formData.type != 2" label="文件" prop="url">
        <UploadFile :model-value="formData.url?formData.url:[]" :file-type="formData.type == 1 ? ['apk'] : []" :limit="1"
          :file-size="formData.type == 1 ? 200 : 50" upload-path="public/download/app" :use-file-name="true" />
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { AppDownloadApi, AppDownloadVO } from '@/api/vr/appdownload'

/** app下载相关 表单 */
defineOptions({ name: 'AppDownloadForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  title: undefined,
  releaseTime: undefined,
  entry: undefined,
  url: undefined,
  type: undefined,
})
const formRules = reactive({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  releaseTime: [{ required: true, message: '发布时间不能为空', trigger: 'blur' }],
  entry: [{ required: true, message: '发布说明不能为空', trigger: 'blur' }],
  url: [{ required: true, message: 'app或者文件url不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类型', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AppDownloadApi.getAppDownload(id)
      if (formData.value.entry) {
        formData.value.entry = JSON.parse(formData.value.entry).map((item: { content: string }) => item.content).join('\n')
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AppDownloadVO
    if (data.entry) {
      data.entry = JSON.stringify(data.entry.split('\n').map((item) => ({ content: item })))
    }
    if (formType.value === 'create') {
      await AppDownloadApi.createAppDownload(data)
      message.success(t('common.createSuccess'))
    } else {
      await AppDownloadApi.updateAppDownload(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    title: undefined,
    releaseTime: undefined,
    entry: undefined,
    url: undefined,
    type: undefined,
  }
  formRef.value?.resetFields()
}
</script>