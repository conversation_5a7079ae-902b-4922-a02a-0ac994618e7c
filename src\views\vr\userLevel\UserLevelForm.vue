<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="800px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="组名称" prop="levelName">
        <el-input v-model="formData.levelName" placeholder="请输入组名称" />
      </el-form-item>
      <el-form-item label="按月付费" prop="payMonth">
        <el-input v-model="formData.payMonth" placeholder="请输入按月付费">
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="按季度付费" prop="paySeason">
        <el-input v-model="formData.paySeason" placeholder="请输入按季度付费">
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="按年付费" prop="payYear">
        <el-input v-model="formData.payYear" placeholder="请输入按年付费">
          <template #append>元</template>
        </el-input>
      </el-form-item>
      <el-form-item label="空间大小" prop="limitSpace">
        <el-input v-model="formData.limitSpace" placeholder="请输入空间大小">
          <template #append>GB</template>
        </el-input>
      </el-form-item>
      <el-form-item label="子账号数量" prop="limitSon">
        <el-input v-model="formData.limitSon" placeholder="请输入子账号数量">
          <template #append>个</template>
        </el-input>
      </el-form-item>
      <el-form-item label="组权配置" prop="privileges">
        <el-checkbox-group v-model="formData.privileges">
          <div class="checkbox-row" style="display: flex; flex-wrap: wrap;">
            <div v-for="item in privilegeList" :key="item.key" style="width: 25%; margin-bottom: 2px;">
              <el-checkbox :label="item.key">{{ item.name }}</el-checkbox>
            </div>
          </div>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="formData.status" clearable placeholder="请选择状态">
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="组权限" prop="privileges">
        <el-checkbox-group v-model="formData.privileges">
          <el-checkbox 
            v-for="item in privilegeList" 
            :key="item.key"
            :label="item.key"
          >
            {{ item.name }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->
      <!-- <el-form-item label="组权限" prop="privileges">
        <el-select v-model="formData.privileges" placeholder="请选择组权限" multiple>
          <el-option v-for="item in privilegeList" :key="item.key" :label="item.name" :value="item.key" />
        </el-select>
      </el-form-item> -->
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { CommonStatusEnum } from '@/utils/constants'
import { UserLevelApi, UserLevelVO, getPrivileges, PrivilegeVO } from '@/api/vr/userLevel'

/** 用户组 表单 */
defineOptions({ name: 'UserLevelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  levelName: undefined,
  limitSpace: 0,
  limitSon: undefined,
  payMonth: undefined,
  paySeason: undefined,
  payYear: undefined,
  privileges: undefined,
  status: CommonStatusEnum.ENABLE,
})
const formRules = reactive({
  levelName: [{ required: true, message: '组名称不能为空', trigger: 'blur' }],
  limitSpace: [{ required: true, message: '空间大小不能为空', trigger: 'blur' }],
  limitSon: [{ required: true, message: '子账号数量不能为空', trigger: 'blur' }],
  payMonth: [{ required: true, message: '按月付费不能为空', trigger: 'blur' }],
  paySeason: [{ required: true, message: '按季度付费不能为空', trigger: 'blur' }],
  payYear: [{ required: true, message: '按年付费不能为空', trigger: 'blur' }],
  // privileges: [{ required: true, message: '组权限不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const privilegeList = ref<PrivilegeVO[]>([])
onMounted(async () => {
  privilegeList.value = await getPrivileges()
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserLevelApi.getUserLevel(id)
      formData.value.limitSpace = formData.value.limitSpace / 1024 / 1024;
    } finally {
      formLoading.value = false
    }
  }

}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UserLevelVO
    data.limitSpace = data.limitSpace * 1024 * 1024;  
    if (formType.value === 'create') {
      await UserLevelApi.createUserLevel(data)
      message.success(t('common.createSuccess'))
    } else {
      await UserLevelApi.updateUserLevel(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    levelName: undefined,
    limitSpace: 0,
    limitSon: undefined,
    payMonth: undefined,
    paySeason: undefined,
    payYear: undefined,
    privileges: undefined,
    status: CommonStatusEnum.ENABLE,
  }
  formRef.value?.resetFields()
}
</script>