import request from '@/config/axios'

// 全景视频项目 VO
export interface VideoVO {
  sort: number // 排序，从小到大
}

// 全景视频项目 API
export const VideoApi = {
  // 查询全景视频项目分页
  getVideoPage: async (params: any) => {
    return await request.get({ url: `/vr/video/page`, params })
  },

  // 查询全景视频项目详情
  getVideo: async (id: number) => {
    return await request.get({ url: `/vr/video/get?id=` + id })
  },

  // 新增全景视频项目
  createVideo: async (data: VideoVO) => {
    return await request.post({ url: `/vr/video/create`, data })
  },

  // 修改全景视频项目
  updateVideo: async (data: VideoVO) => {
    return await request.put({ url: `/vr/video/update`, data })
  },

  // 删除全景视频项目
  deleteVideo: async (id: number) => {
    return await request.delete({ url: `/vr/video/delete?id=` + id })
  },

  // 导出全景视频项目 Excel
  exportVideo: async (params) => {
    return await request.download({ url: `/vr/video/export-excel`, params })
  },
}
