<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
      @submit.prevent
    >
      <el-form-item label="文章标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入文章标题"
          clearable
          @keydown.enter.prevent="handleQuery"
          @keyup.enter.prevent
          class="!w-200px"
        />
      </el-form-item>
      
      <el-form-item label="分类" prop="catId">
        <el-select
          v-model="queryParams.catId"
          placeholder="请选择分类"
          clearable
          class="!w-180px"
        >
          <el-option
            v-for="item in catList"
            :key="item.id"
            :label="item.catName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:article:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('cat')"
          v-hasPermi="['vr:article:cat']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 分类
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="id" align="center" prop="id" width="80px"/>
      <el-table-column width="400px" label="文章标题" align="center" prop="title">
        <template #default="scope">
          <el-button
            :type="'primary'"
            text
            @click="openArticle(scope.row.id)"
            >{{ scope.row.title }}
          </el-button>
        </template>
      </el-table-column>
      
      <el-table-column label="封面" align="center" prop="thumb" >
        <template #default="scope">
          <el-image
            :src="`${appStore.cdnUrl}${scope.row.thumb}?x-oss-process=image/resize,m_fill,h_300,w_300`"
            fit="cover"
            style="width: 30px; height: 30px"
            :preview-src-list="[`${appStore.cdnUrl}${scope.row.thumb}`]"
            :initial-index="0"
            :preview-teleported="true"
            :z-index="3000"
          />
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" prop="catId">
        <template #default="scope">
          {{ catList.find(item => item.id === scope.row.catId)?.catName }}
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="createtime"
        :formatter="dateFormatter"
        width="180px"
      />
      
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:article:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vr:article:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ArticleForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app';
import { dateFormatter } from '@/utils/formatTime'
import { ArticleApi, ArticleVO, ArticleCatVO } from '@/api/vr/article'
import ArticleForm from './ArticleForm.vue'

/** 文章详情 列表 */
defineOptions({ name: 'ArticleList' })

const appStore = useAppStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ArticleVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
  catId: undefined,
})
const queryFormRef = ref() // 搜索的表单
const catList = ref<ArticleCatVO[]>([]) // 分类列表

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ArticleApi.getArticlePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 获取分类列表 */
const getCatList = async () => {
  const data = await ArticleApi.getArticleCatList()
  catList.value = data
}

/** 搜索按钮操作 */
const handleQuery = async (e?: Event) => {
  if (e) {
    e.preventDefault()
  }
  queryParams.pageNo = 1
  await getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ArticleApi.deleteArticle(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

const openArticle = (articleId: number) => {
  // 打开新窗口，注意 appStore.websiteUrl 需要确保正确
  window.open(`${appStore.websiteUrl}/article/${articleId}.html`, '_blank');
}

/** 初始化 **/
onMounted(() => {
  getList()
  getCatList()
})
</script>