<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="套餐名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入套餐名称" />
      </el-form-item>
      <el-form-item label="价格" prop="price">
        <el-input v-model="formData.price" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="套餐详情" prop="content">
        <Editor v-model="formData.content" height="150px" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入排序" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { SystemPackageApi, SystemPackageVO } from '@/api/vr/systempackage'
import { useAppStore } from '@/store/modules/app'
/** 系统套餐 表单 */
defineOptions({ name: 'SystemPackageForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const appStore = useAppStore() // 应用商店

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined as string | undefined,
  price: undefined as string | undefined,
  content: undefined as string | undefined,
  sort: undefined as number | undefined,
})
const formRules = reactive({
  name: [{ required: true, message: '套餐名称不能为空', trigger: 'blur' }],
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '套餐详情不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SystemPackageApi.getSystemPackage(id)
      // 处理富文本中的图片链接
      formData.value.content = handleRichTextImages(formData.value.content)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SystemPackageVO
    if (formType.value === 'create') {
      await SystemPackageApi.createSystemPackage(data)
      message.success(t('common.createSuccess'))
    } else {
      await SystemPackageApi.updateSystemPackage(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 处理富文本中的图片链接 */
const handleRichTextImages = (content: string | undefined) => {
  if (!content) return content
  // 使用正则表达式匹配 img 标签的 src 属性
  return content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, src) => {
    if (!src.startsWith('http')) {
      return match.replace(src, appStore.cdnUrl + src)
    }
    return match
  })
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    price: undefined,
    content: undefined,
    sort: undefined,
  }
  formRef.value?.resetFields()
}
</script>