import request from '@/config/axios'

// 相机产品介绍 VO
export interface ProductIntroductionVO {
  title: string // 标题
  keywords: string // 关键词
  content: string // 内容
  sort: number // 排序规则，越小越靠前
}

// 相机产品介绍 API
export const ProductIntroductionApi = {
  // 查询相机产品介绍分页
  getProductIntroductionPage: async (params: any) => {
    return await request.get({ url: `/vr/product-introduction/page`, params })
  },

  // 查询相机产品介绍详情
  getProductIntroduction: async (id: number) => {
    return await request.get({ url: `/vr/product-introduction/get?id=` + id })
  },

  // 新增相机产品介绍
  createProductIntroduction: async (data: ProductIntroductionVO) => {
    return await request.post({ url: `/vr/product-introduction/create`, data })
  },

  // 修改相机产品介绍
  updateProductIntroduction: async (data: ProductIntroductionVO) => {
    return await request.put({ url: `/vr/product-introduction/update`, data })
  },

  // 删除相机产品介绍
  deleteProductIntroduction: async (id: number) => {
    return await request.delete({ url: `/vr/product-introduction/delete?id=` + id })
  },

  // 导出相机产品介绍 Excel
  exportProductIntroduction: async (params) => {
    return await request.download({ url: `/vr/product-introduction/export-excel`, params })
  },
}
