<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="编码" prop="code">
        <el-input
          v-model="queryParams.code"
          placeholder="请输入编码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="激活时间" prop="activeTime">
        <el-date-picker
          v-model="queryParams.activeTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:machine-code:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 批量导入
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:machine-code:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"       @filter-change="handleFilterChange"    >
      <el-table-column label="机器码" align="center" prop="code" >
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.code }}</span>
            <el-button 
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.code)">
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      
      <el-table-column label="是否激活" align="center" prop="status"
      :filters="[
          { text: '是', value: 'true' },
          { text: '否', value: 'false' }
        ]"
        :filter-multiple="false"
        column-key="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === 1" type="success">是</el-tag>
          <el-tag v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="激活时间"
        align="center"
        prop="activeTime"
        sortable
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column width="180px" label="激活用户" align="center" prop="userName"/>
      <el-table-column label="用户Id" align="center" prop="uid" sortable>
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.uid || '' }}</span>
            <el-button 
              v-if="scope.row.uid"
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.uid)"
            >
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column
        label="导入时间"
        align="center"
        prop="createTime"
        sortable
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:machine-code:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vr:machine-code:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <MachineCodeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { MachineCodeApi, MachineCodeVO } from '@/api/vr/machinecode'
import MachineCodeForm from './MachineCodeForm.vue'
import { useUserStore } from '@/store/modules/user';

/** 机器码 列表 */
defineOptions({ name: 'MachineCode' })

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<MachineCodeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<{
  pageNo: number
  pageSize: number
  code?: string
  activeTime: any[]
  status?: boolean
  tenantId?: number
}>({
  pageNo: 1,
  pageSize: 10,
  code: undefined,
  activeTime: [],
  status: undefined,
  tenantId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await MachineCodeApi.getMachineCodePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await MachineCodeApi.deleteMachineCode(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await MachineCodeApi.exportMachineCode(queryParams)
    download.excel(data, '机器码.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

// 处理筛选
const handleFilterChange = (values) => {
  queryParams.status = convertBoolStrValueToBool(values['status']?.[0])
  handleQuery()
}

const convertBoolStrValueToBool = (value) => {
  if (value === undefined) return undefined
  return JSON.parse(value)
}

const clipboard = (text: string | number) => {
  navigator.clipboard.writeText(String(text))
  message.success('复制成功')
}
/** 初始化 **/
onMounted(() => {
  getList()
})
</script>