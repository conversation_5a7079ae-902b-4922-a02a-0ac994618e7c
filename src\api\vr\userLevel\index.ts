import request from '@/config/axios'

// 用户组 VO
export interface UserLevelVO {
  id: number // 组id，1为系统默认，不可删除
  levelName: string // 组名称
  limitSpace: number // 空间大小
  limitSon: number // 子账号数量
  payMonth: number // 按月付费
  paySeason: number // 按季度付费
  payYear: number // 按年付费
  privileges: [] // 组权限
}

// 用户组权限 VO
export interface PrivilegeVO {
  key: string // 权限key
  name: string // 权限名称
}

// 用户组 API
export const UserLevelApi = {
  // 查询用户组分页
  getUserLevelPage: async (params: any) => {
    return await request.get({ url: `/vr/user-level/page`, params })
  },
  

  // 查询用户组详情
  getUserLevel: async (id: number) => {
    return await request.get({ url: `/vr/user-level/get?id=` + id })
  },

  // 新增用户组
  createUserLevel: async (data: UserLevelVO) => {
    return await request.post({ url: `/vr/user-level/create`, data })
  },

  // 修改用户组
  updateUserLevel: async (data: UserLevelVO) => {
    return await request.put({ url: `/vr/user-level/update`, data })
  },

  // 删除用户组
  deleteUserLevel: async (id: number) => {
    return await request.delete({ url: `/vr/user-level/delete?id=` + id })
  },

  // 导出用户组 Excel
  exportUserLevel: async (params) => {
    return await request.download({ url: `/vr/user-level/export-excel`, params })
  },

}

 // 查询用户组列表
export const getUserLevelAll = () => {
  return request.get({ url: '/vr/user-level/getAll' })
}

// 查询用户组权限
export const getPrivileges = () => {
  return request.get({ url: '/vr/user-level/getPrivileges' })
}