<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="500px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
    <el-form-item label="广告名称" prop="adName">
      <el-input v-model="formData.adName" placeholder="请输入广告名称" />
    </el-form-item>
    
    <el-form-item label="广告位置" prop="position">
      <el-select v-model="formData.position" placeholder="请选择广告位置" class="!w-240px">
        <el-option
          v-for="dict in getIntDictOptions(DICT_TYPE.VR_AD_POSITION)" 
          :key="dict.value"
          :label="dict.label"
          :value="dict.value"
          :disabled="formType === 'update'"
        />
      </el-select>
    </el-form-item>
    <el-form-item label="图片" prop="adContent">
      <UploadImg v-model="formData.adContent" height="200px" width="200px" :preview="true" upload-path="public/image"/>
    </el-form-item>
      <el-form-item label="广告链接" prop="adLink">
        <el-input v-model="formData.adLink" placeholder="请输入广告链接" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { AdApi, AdVO } from '@/api/vr/ad'
import { DICT_TYPE } from '@/utils/dict'
import { useAppStore } from '@/store/modules/app'
import { getIntDictOptions } from '@/utils/dict'

/** 站点广告 表单 */
defineOptions({ name: 'AdForm' })



const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const appStore = useAppStore()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  adLink: undefined,
  adName: undefined,
  adContent: undefined as string | undefined,
  position: undefined as number | undefined,
})
const formRules = reactive({
  adLink: [{ required: true, message: '广告链接不能为空', trigger: 'blur' }],
  adName: [{ required: true, message: '广告名称不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await AdApi.getAd(id)
      if (formData.value.adContent && !formData.value.adContent.startsWith('http')) {
        formData.value.adContent = appStore.cdnUrl + formData.value.adContent
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as AdVO
    if (data.adContent && data.adContent.startsWith(appStore.cdnUrl)) {
      data.adContent = data.adContent.replace(appStore.cdnUrl, '')
    }
    if (formType.value === 'create') {
      await AdApi.createAd(data)
      message.success(t('common.createSuccess'))
    } else {
      await AdApi.updateAd(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    adLink: undefined,
    adName: undefined,
    adContent: undefined as string | undefined,
    position: undefined as number | undefined,
  }
  formRef.value?.resetFields()
}
</script>