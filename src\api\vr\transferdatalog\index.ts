import request from '@/config/axios'

// 数据转移记录 VO
export interface TransferDataLogVO {
}

// 数据转移记录 API
export const TransferDataLogApi = {
  // 查询数据转移记录分页
  getTransferDataLogPage: async (params: any) => {
    return await request.get({ url: `/vr/transfer-data-log/page`, params })
  },

  // 查询数据转移记录详情
  getTransferDataLog: async (id: number) => {
    return await request.get({ url: `/vr/transfer-data-log/get?id=` + id })
  },

  // 新增数据转移记录
  createTransferDataLog: async (data: TransferDataLogVO) => {
    return await request.post({ url: `/vr/transfer-data-log/create`, data })
  },

  // 修改数据转移记录
  updateTransferDataLog: async (data: TransferDataLogVO) => {
    return await request.put({ url: `/vr/transfer-data-log/update`, data })
  },

  // 删除数据转移记录
  deleteTransferDataLog: async (id: number) => {
    return await request.delete({ url: `/vr/transfer-data-log/delete?id=` + id })
  },

  // 导出数据转移记录 Excel
  exportTransferDataLog: async (params) => {
    return await request.download({ url: `/vr/transfer-data-log/export-excel`, params })
  },
}
