import request from '@/config/axios'

// 全景项目作品 VO
export interface WorksmainVO {
  pkWorksMain: number // 项目id
  name: string // 项目名称
  profile: string // 场景简介
  thumbPath: string // 缩略图
  recommend: boolean // 是否推荐 0 不推荐 1推荐
  userRecommend: string // 用户推荐
  tenantId: number // 租户id
  tenantName: string // 租户名称
}

// 全景项目作品 API
export const WorksmainApi = {
  // 查询全景项目作品分页
  getWorksmainPage: async (params: any) => {
    return await request.get({ url: `/vr/worksmain/page`, params })
  },

  // 查询全景项目作品详情
  getWorksmain: async (id: number) => {
    return await request.get({ url: `/vr/worksmain/get?id=` + id })
  },

  // 新增全景项目作品
  createWorksmain: async (data: WorksmainVO) => {
    return await request.post({ url: `/vr/worksmain/create`, data })
  },

  // 修改全景项目作品
  updateWorksmain: async (data: WorksmainVO) => {
    return await request.put({ url: `/vr/worksmain/update`, data })
  },

  // 删除全景项目作品
  deleteWorksmain: async (id: number) => {
    return await request.delete({ url: `/vr/worksmain/delete?id=` + id })
  },

  // 导出全景项目作品 Excel
  exportWorksmain: async (params) => {
    return await request.download({ url: `/vr/worksmain/export-excel`, params })
  },
}
