<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" @cancel="closeDialog">
    <div class="mb-20px">
      <p class="mb-10px">请选择要将会员转移至的经销商：</p>
      <div style="max-height: 300px; overflow-y: auto;">
        <el-radio-group v-model="selectedDealerId">
          <el-space direction="vertical" alignment="flex-start" :size="10" style="width: 100%;">
            <el-radio 
              v-for="item in dealerOptions" 
              :key="item.id" 
              :label="item.id"
            >
              {{ item.name }}
            </el-radio>
          </el-space>
        </el-radio-group>
      </div>
    </div>

    <div class="mb-20px">
      <p class="mb-10px">请输入需要转移到的会员手机号：</p>
      <el-input 
        v-model="targetPhone" 
        placeholder="请输入会员手机号" 
        clearable
        :maxlength="11"
        show-word-limit
      >
        <template #prefix>
          <Icon icon="ep:phone" />
        </template>
      </el-input>
      <p class="mt-5px text-gray-400 text-sm">后续会将数据转移到该手机号对应的会员账号上</p>
    </div>
    
    <div class="mb-20px">
      <p class="mb-10px">验证码：</p>
      <div class="flex">
        <el-input 
          v-model="verificationCode" 
          placeholder="请输入验证码" 
          clearable
          class="flex-1 mr-10px"
          :maxlength="6"
          show-word-limit
        >
          <template #prefix>
            <Icon icon="ep:key" />
          </template>
        </el-input>
        <el-button 
          type="primary" 
          @click="sendCode"
          :disabled="sendCodeLoading || countDown > 0"
          :loading="sendCodeLoading"
        >
          {{ countDown > 0 ? `${countDown}秒后重试` : '发送验证码' }}
        </el-button>
      </div>
      <p class="mt-5px text-gray-400 text-sm">请从用户处获取6位数字验证码</p>
    </div>

    <div v-if="errorMessage" class="my-10px">
      <el-alert
        :title="errorMessage"
        type="error"
        :closable="false"
        show-icon
      />
    </div>

    <div v-if="confirmStep === 1" class="my-10px">
      <el-alert
        title="确定要转移该会员到选定的经销商吗？此操作不可逆！"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>
    <div v-if="confirmStep === 1" class="my-10px">
      <el-alert
        title="注意：转移时只会转移会员的相关作品，不会转移等级、扩容等权益，请谨慎操作"
        type="warning"
        :closable="false"
        show-icon
      />
    </div>

    <template #footer>
      <el-button @click="closeDialog">取消</el-button>
      <el-button 
        v-if="confirmStep === 0" 
        type="primary" 
        :disabled="!selectedDealerId || !isValidPhone || !isValidCode" 
        @click="confirmStep = 1"
      >
        确定
      </el-button>
      <el-button 
        v-else 
        type="danger" 
        @click="handleTransfer"
        :loading="transferLoading"
      >
        确认转移
      </el-button>
    </template>
  </Dialog>
</template>

<script setup lang="ts">
import { PropType } from 'vue'
import { UserApi } from '@/api/vr/user'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'TransferDealerForm' })

const dialogVisible = ref(false)
const dialogTitle = ref('数据转移')
const userId = ref<number>()
const selectedDealerId = ref<number>()
const currentTenantId = ref<number>()
const targetPhone = ref<string>('')
const errorMessage = ref('')
const confirmStep = ref(0)
const transferLoading = ref(false)
const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const dealerOptions = computed(() => userStore.getAllTenants)

// 验证码相关
const verificationCode = ref<string>('')
const sendCodeLoading = ref(false)
const countDown = ref(0)
const countDownTimer = ref<number | null>(null)

// 验证手机号是否有效
const isValidPhone = computed(() => {
  if (!targetPhone.value) return false
  // 简单的中国手机号验证规则
  return /^1[3-9]\d{9}$/.test(targetPhone.value)
})

// 验证验证码是否有效
const isValidCode = computed(() => {
  if (!verificationCode.value) return false
  // 6位数字验证码
  return /^\d{6}$/.test(verificationCode.value)
})

// 暴露方法给父组件调用
const emit = defineEmits(['success'])

/**
 * 打开弹窗
 * @param id 会员ID
 */
const open = async (id: number) => {
  resetForm()
  userId.value = id
  dialogVisible.value = true
  
  try {
    // 获取用户当前的经销商ID
    const userInfo = await UserApi.getUser(id)
    if (userInfo) {
      currentTenantId.value = userInfo.tenantId
    }
  } catch (error) {
    console.error('获取用户信息失败:', error)
    errorMessage.value = '获取用户信息失败'
  }
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
  dialogVisible.value = false
  resetForm()
  // 清除定时器
  if (countDownTimer.value) {
    clearInterval(countDownTimer.value)
    countDownTimer.value = null
  }
}

/**
 * 重置表单
 */
const resetForm = () => {
  userId.value = undefined
  selectedDealerId.value = undefined
  currentTenantId.value = undefined
  targetPhone.value = ''
  verificationCode.value = ''
  errorMessage.value = ''
  confirmStep.value = 0
  transferLoading.value = false
}

/**
 * 发送验证码
 */
const sendCode = async () => {
  if (!userId.value) {
    errorMessage.value = '用户ID不能为空'
    return
  }
  
  try {
    sendCodeLoading.value = true
    await UserApi.sendTransferDealerCode(userId.value)
    // 发送成功后清除错误信息
    errorMessage.value = ''
    alert('验证码发送成功，请联系用户接收短信并获取6位数字验证码')
    
    // 开始倒计时
    countDown.value = 60
    countDownTimer.value = window.setInterval(() => {
      if (countDown.value > 0) {
        countDown.value--
      } else {
        if (countDownTimer.value) {
          clearInterval(countDownTimer.value)
          countDownTimer.value = null
        }
      }
    }, 1000)
    
  } catch (error: any) {
    console.error('验证码发送失败:', error)
    errorMessage.value = error?.msg || '验证码发送失败'
  } finally {
    sendCodeLoading.value = false
  }
}

/**
 * 提交数据转移
 */
const handleTransfer = async () => {
  if (!userId.value || !selectedDealerId.value) {
    errorMessage.value = '请选择要转移到的经销商'
    return
  }
  
  if (!isValidPhone.value) {
    errorMessage.value = '请输入有效的会员手机号'
    return
  }
  
  if (!isValidCode.value) {
    errorMessage.value = '请输入有效的6位数字验证码'
    return
  }
  
  transferLoading.value = true
  errorMessage.value = ''
  
  try {
    // 调用API进行数据转移，包含目标手机号和验证码
    var res = await UserApi.transferUserDealer(userId.value, selectedDealerId.value, targetPhone.value, verificationCode.value)
    console.log(res)
      alert('会员数据转移成功：' + res + "\n全景项目文件正在执行转移，可能需要几分钟，期间请勿再次转移")
      // 关闭弹窗
      closeDialog()
      // 通知父组件刷新列表
      emit('success')
  } catch (error: any) {
    console.error('会员数据转移失败:')
    console.error(error)
    errorMessage.value = error?.msg || '会员数据转移失败'
  } finally {
    transferLoading.value = false
  }
}

// 向外暴露方法
defineExpose({
  open
})
</script> 