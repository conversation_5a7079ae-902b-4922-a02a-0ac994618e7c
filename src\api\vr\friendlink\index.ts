import request from '@/config/axios'

// 友情链接 VO
export interface FriendlinkVO {
  id: number // id
  websiteName: string // 标题
  websiteLink: string // 链接
}

// 友情链接 API
export const FriendlinkApi = {
  // 查询友情链接分页
  getFriendlinkPage: async (params: any) => {
    return await request.get({ url: `/vr/friendlink/page`, params })
  },

  // 查询友情链接详情
  getFriendlink: async (id: number) => {
    return await request.get({ url: `/vr/friendlink/get?id=` + id })
  },

  // 新增友情链接
  createFriendlink: async (data: FriendlinkVO) => {
    return await request.post({ url: `/vr/friendlink/create`, data })
  },

  // 修改友情链接
  updateFriendlink: async (data: FriendlinkVO) => {
    return await request.put({ url: `/vr/friendlink/update`, data })
  },

  // 删除友情链接
  deleteFriendlink: async (id: number) => {
    return await request.delete({ url: `/vr/friendlink/delete?id=` + id })
  },

  // 导出友情链接 Excel
  exportFriendlink: async (params) => {
    return await request.download({ url: `/vr/friendlink/export-excel`, params })
  },
}
