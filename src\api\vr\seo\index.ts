import request from '@/config/axios'

// 网站SEO 配置 VO
export interface SeoVO {
  title: string // 页面标题
  keywords: string // 关键词
  description: string // 页面简介
}

// 网站SEO 配置 API
export const SeoApi = {
  // 查询网站SEO 配置分页
  getSeoPage: async (params: any) => {
    return await request.get({ url: `/vr/seo/page`, params })
  },

  // 查询网站SEO 配置详情
  getSeo: async (id: number) => {
    return await request.get({ url: `/vr/seo/get?id=` + id })
  },

  // 新增网站SEO 配置
  createSeo: async (data: SeoVO) => {
    return await request.post({ url: `/vr/seo/create`, data })
  },

  // 修改网站SEO 配置
  updateSeo: async (data: SeoVO) => {
    return await request.put({ url: `/vr/seo/update`, data })
  },

  // 删除网站SEO 配置
  deleteSeo: async (id: number) => {
    return await request.delete({ url: `/vr/seo/delete?id=` + id })
  },

  // 导出网站SEO 配置 Excel
  exportSeo: async (params) => {
    return await request.download({ url: `/vr/seo/export-excel`, params })
  },
}
