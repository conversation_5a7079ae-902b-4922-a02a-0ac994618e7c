<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="问题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入问题" />
      </el-form-item>
      <el-form-item label="视频url" prop="videourl">
        <UploadFile
          v-model="formData.videourl"
          :file-type="['video/mp4']"
          :limit="1"
          :upload-path="`/public/help/video`"
          :file-size="100"
          class="min-w-80px"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number v-model="formData.sort" placeholder="请输入排序" :min="0" :max="999" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { HelpGroupApi } from '@/api/vr/help'
import { useAppStore } from '@/store/modules/app';

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const appStore = useAppStore()
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  title: undefined as string | undefined,
  videourl: undefined as string | undefined,
  sort: undefined as number | undefined,
  gid: undefined as number | undefined
})
const formRules = reactive({
  title: [{ required: true, message: '问题不能为空', trigger: 'blur' }],
  videourl: [{ required: true, message: '视频url不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number, gid: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.gid = gid
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await HelpGroupApi.getHelp(id)
      if (formData.value.videourl && !formData.value.videourl.includes(appStore.cdnUrl)) {
        formData.value.videourl = appStore.cdnUrl + formData.value.videourl
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  if (formData.value.videourl) {
    formData.value.videourl = formData.value.videourl.replace(appStore.cdnUrl, '')
  }
  try {
    const data = formData.value
    if (formType.value === 'create') {
      await HelpGroupApi.createHelp(data)
      message.success(t('common.createSuccess'))
    } else {
      await HelpGroupApi.updateHelp(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    title: undefined,
    videourl: undefined,
    sort: undefined,
    gid: undefined
  }
  formRef.value?.resetFields()
}
</script>