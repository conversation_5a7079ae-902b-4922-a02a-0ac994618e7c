<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="订单号" prop="orderSn">
        <el-input
          v-model="queryParams.orderSn"
          placeholder="请输入订单号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="会员账号" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入会员账号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="会员名称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入会员名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input-number 
          v-model="queryParams.startAmount" 
          placeholder="最小金额" 
          :min="0"
          class="!w-120px"
        />
        <span class="mx-2">-</span>
        <el-input-number
          v-model="queryParams.endAmount"
          placeholder="最大金额"
          :min="0" 
          class="!w-120px"
        />
      </el-form-item>
      <el-form-item label="时间" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          value-format="YYYY-MM-DD HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
          class="!w-220px"
          @change="handleDateChange"
/>
      </el-form-item>
      <el-form-item label="订单类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择订单类型"
          clearable
          multiple
          collapse-tags
          collapse-tags-tooltip
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VR_ORDER_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="订单状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="请选择订单状态"
          clearable
          class="!w-240px"
        >
          <el-option value="1" label="已支付" />
          <el-option value="0" label="待支付" />
        </el-select>
      </el-form-item>
      <el-form-item label="结算状态" prop="settlementState">
        <el-select
          v-model="queryParams.settlementState"
          placeholder="请选择结算状态"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.VR_ORDER_SETTLEMENT_STATE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery" :disabled="isSettling">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery" :disabled="isSettling">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:recharge:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          :disabled="isSettling"
          v-hasPermi="['vr:recharge:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
        <el-button
          type="warning"
          plain
          @click="handleSettlement"
          v-hasPermi="['vr:recharge:settlement']"
          :disabled="isSettling"
        >
          <Icon icon="ep:money" class="mr-5px" /> 结算
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table 
      ref="tableRef"
      v-loading="loading" 
      :data="list" 
      :stripe="true" 
      fit
      @selection-change="handleSelectionChange"
    >
      <el-table-column 
        v-if="isSettling" 
        type="selection" 
        width="55" 
        :selectable="selectable"
      />
      <el-table-column label="订单号" align="center" prop="orderSn" width="220px">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.orderSn }}</span>
            <el-button 
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.orderSn)">
              <Icon icon="ep:copy-document" />
            </el-button> 
          </div>
          <span v-if="scope.row.parentOrderSn" class="text-sm text-gray-500">父订单号：{{ scope.row.parentOrderSn }}</span>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column label="会员账号" align="center" prop="phone" width="150px">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.phone }}</span>
            <el-button 
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.phone)">
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="会员昵称" align="center" prop="nickname">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.nickname }}</span>
            <el-button 
              link
              type="primary"
              class="ml-5px"
              @click="clipboard(scope.row.nickname)">
              <Icon icon="ep:copy-document" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payMethod" width="150px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VR_PAY_METHOD" :value="scope.row.payMethod" />
        </template>
      </el-table-column>
      <el-table-column label="订单类型" align="center" prop="type" width="150px">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <dict-tag :type="DICT_TYPE.VR_ORDER_TYPE" :value="scope.row.type" />
            <el-button
              v-if="scope.row.type === 9"
              link
              type="primary"
              class="ml-5px"
              @click="handleViewRenewal(scope.row.id)"
            >
              <Icon icon="ep:calendar" />
            </el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="交易金额" align="center">
        <template #default="scope">
          <div>
            {{ scope.row.status === 1 ? '已支付' : '待支付' }} ￥{{ scope.row.amount }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="结算状态" align="center" prop="settlementState" width="150px">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VR_ORDER_SETTLEMENT_STATE" :value="scope.row.settlementState" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="支付状态" align="center" prop="status">
        <template #default="scope">
          <el-tag :type="scope.row.status === 1 ? 'success' : 'warning'">
            {{ scope.row.status === 1 ? '已支付' : '待支付' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <div v-if="isSettling" class="settlement-footer">
      <div class="settlement-actions">
        <el-button @click="cancelSettlement">取消</el-button>
        <el-button type="primary" @click="confirmSettlement" :disabled="!selectedRows.length">
          确认结算
        </el-button>
      </div>
      <div class="settlement-info">
        已选择金额：<span class="amount">￥{{ selectedTotalAmount }}</span>
      </div>
    </div>

    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
      :disabled="isSettling"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <RechargeForm ref="formRef" @success="getList" />

  <!-- 结算确认弹窗 -->
  <el-dialog
    v-model="settlementDialogVisible"
    title="结算确认"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="settlement-confirm-content">
      <div class="confirm-item">
        <span class="label">已选择订单数：</span>
        <span class="value">{{ selectedRows.length }} 笔</span>
      </div>
      <div class="confirm-item">
        <span class="label">结算总金额：</span>
        <span class="value amount">￥{{ selectedTotalAmount }}</span>
      </div>
    </div>
    <template #footer>
      <el-button @click="settlementDialogVisible = false">取消</el-button>
      <el-button type="primary" @click="submitSettlement">确认结算</el-button>
    </template>
  </el-dialog>

  <!-- 续期信息弹窗 -->
  <el-dialog
    v-model="renewalDialogVisible"
    title="续期信息"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="renewal-info">
      <div class="info-item">
        <span class="label">全景项目：</span>
        <el-button 
          link 
          type="primary" 
          @click="handleOpenTour(renewalInfo.worksmain?.viewUuid)"
        >
          {{ renewalInfo.worksmain?.name }}
        </el-button>
      </div>
      <div class="info-item">
        <span class="label">本次续期过期时间：</span>
        <span class="value">{{ formatDateTime(renewalInfo.expireTime) }}</span>
      </div>
    </div>
    <template #footer>
      <el-button @click="renewalDialogVisible = false">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter, formatDate } from '@/utils/formatTime'
import download from '@/utils/download'
import { RechargeApi, RechargeVO } from '@/api/vr/order'
import RechargeForm from './RechargeForm.vue'
import { useUserStore } from '@/store/modules/user'
import { useAppStore } from '@/store/modules/app'
import { ElTable } from 'element-plus'

/** 订单 列表 */
defineOptions({ name: 'Recharge' })
const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const appStore = useAppStore()

const loading = ref(true) // 列表的加载中
const list = ref<RechargeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  uid: undefined,
  orderSn: undefined,
  nickname: undefined,
  phone: undefined,
  startAmount: undefined,
  endAmount: undefined,
  startCreateTime: undefined,
  endCreateTime: undefined,
  type: undefined,
  status: undefined,
  createTime: [],
  tenantId: undefined,
  settlementState: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

const isSettling = ref(false)
const selectedRows = ref<RechargeVO[]>([])
const tableRef = ref<InstanceType<typeof ElTable>>()
const renewalDialogVisible = ref(false)
const renewalInfo = ref<any>({})

// 计算已选择订单的总金额
const selectedTotalAmount = computed(() => {
  return selectedRows.value.reduce((sum, row) => sum + Number(row.amount), 0).toFixed(2)
})

// 进入结算状态
const handleSettlement = () => {
  isSettling.value = true
  selectedRows.value = []
  // 清空之前的选择
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 取消结算
const cancelSettlement = () => {
  isSettling.value = false
  selectedRows.value = []
  if (tableRef.value) {
    tableRef.value.clearSelection()
  }
}

// 确认结算
const settlementDialogVisible = ref(false)

// 修改确认结算方法
const confirmSettlement = () => {
  settlementDialogVisible.value = true
}

// 新增提交结算方法
const submitSettlement = async () => {
  try {
    const ids = selectedRows.value.map(row => row.id)
    await RechargeApi.settleOrders(ids)
    message.success('结算成功，正在刷新列表')
    settlementDialogVisible.value = false
    cancelSettlement()
    await getList()
  } catch (error) {
    message.error('结算失败')
  }
}

// 表格选择事件处理
const handleSelectionChange = (rows: RechargeVO[]) => {
  // 只保留已支付的订单
  selectedRows.value = rows.filter(row => row.status === 1 && row.settlementState === 0)
}

// 表格行选择限制
const selectable = (row: RechargeVO) => {
  return row.status === 1 && row.settlementState === 0 // 只有已支付且未结算的订单可以选择
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await RechargeApi.getRechargePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  queryParams.startAmount = undefined
  queryParams.endAmount = undefined
  queryParams.startCreateTime = undefined
  queryParams.endCreateTime = undefined
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await RechargeApi.exportRecharge(queryParams)
    download.excel(data, '订单.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 时间选择器操作 */
const handleDateChange = () => {
  queryParams.startCreateTime = queryParams.createTime[0]
  queryParams.endCreateTime = queryParams.createTime[1]
}

/** 复制按钮操作 */
const clipboard = (text: string | number) => {
  navigator.clipboard.writeText(String(text))
  message.success('复制成功')
}

/** 查看续期信息 */
const handleViewRenewal = async (orderId: number) => {
  try {
    const res = await RechargeApi.getRenewalInfo(orderId)
    renewalInfo.value = res
    renewalDialogVisible.value = true
  } catch (error) {
    message.error('获取续期信息失败')
  }
}

/** 打开全景项目 */
const handleOpenTour = (viewUuid: string) => {
  if (viewUuid) {
    window.open(`${appStore.websiteUrl}/tour/${viewUuid}`, '_blank')
  }
}

/** 格式化日期时间 */
const formatDateTime = (dateTime: string) => {
  if (!dateTime) return ''
  return formatDate(new Date(dateTime))
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

<style scoped>
.settlement-footer {
  margin-top: 16px;
  padding: 16px;
  background-color: #f5f7fa;
  display: flex;
  /* justify-content: space-between; */
  align-items: center;
}

.settlement-info .amount {
  color: #f56c6c;
  font-size: 18px;
  font-weight: bold;
  margin-left: 8px;
}

.settlement-actions {
  display: flex;
  gap: 8px;
  margin-right: 24px; /* 添加右侧间距 */
}

.settlement-confirm-content {
  padding: 20px 0;
}

.confirm-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.confirm-item:last-child {
  margin-bottom: 0;
}

.confirm-item .label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
}

.confirm-item .value {
  font-size: 16px;
  font-weight: bold;
}

.confirm-item .amount {
  color: #f56c6c;
  font-size: 18px;
}

.renewal-info {
  padding: 20px 0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-size: 14px;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-item .value {
  font-size: 14px;
  color: #303133;
}
</style>