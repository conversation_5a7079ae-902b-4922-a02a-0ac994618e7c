<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="740px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      v-loading="formLoading"
    >

      <el-alert
        title="提示"
        type="warning"
        description="该功能为平台外部支付的用户扩充容量,请务必如实填写用户支付的金额"
        :closable="false"
        show-icon
         />
        <el-radio-group v-model="formData.spacePresetId">
          <div class="space-preset-list">
            <div class="space-preset-list">
              <div
                v-for="item in spacePresetList"
                :key="item.id"
                class="space-preset-item"
                :class="{'space-preset-selected': formData.spacePresetId === item.id}"
                @click="selectPreset(item)"
              >
                <el-card class="box-card" shadow="hover">
                  <div class="space-info">{{ item.space }}GB/年</div>
                  <div class="price-info">¥{{ item.money }}</div>
                </el-card>
              </div>
            </div>
          </div>
        </el-radio-group>
        <div class="amount-tip" v-if="formData.spacePresetId" style="margin-bottom: 10px;">
          <el-alert
            title="请务必如实填写用户支付的金额"
            type="error"
            :closable="false"
          />
        </div>
        <el-input v-model="formData.amount" placeholder="请填写支付金额" />
   
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { UserApi, UpdateUserSpaceVO } from '@/api/vr/user'
import { SpacePresetApi, SpacePresetVO } from '@/api/vr/spacepreset'

/** 用户空间 表单 */
defineOptions({ name: 'UserSpaceForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('添加容量') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: -1,
  spacePresetId: 0,
  amount: 0,
})

const formRules = reactive({
  spacePresetId: [{ required: true, message: '请选择套餐', trigger: 'blur' }],
  amount: [{ required: true, message: '线下支付金额不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const spacePresetList = ref([] as SpacePresetVO[]) // 容量预设套餐列表

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  resetForm()
  if (id) {
    formData.value.id = id
  }
  // 加载容量预设套餐列表
  spacePresetList.value = await SpacePresetApi.getSpacePresetList()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗


/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UpdateUserSpaceVO
    await UserApi.updateUserSpace(data)
    message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 选择预设套餐 */
const selectPreset = (item: SpacePresetVO) => {
  formData.value.spacePresetId = item.id
  formData.value.amount = item.money
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: -1,
    spacePresetId: 0,
    amount: 0,
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.space-preset-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.space-preset-item {
  height: auto;
  margin-right: 0;
}

.space-preset-item .el-card {
  width: 160px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;  /* 添加过渡效果 */
}

/* 添加选中状态的样式 */
.space-preset-item.space-preset-selected .el-card {
  border: 2px solid #f56c6c;
  background-color: #fff8f8;
}

.space-info {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.price-info {
  color: #f56c6c;
  font-size: 16px;
}
</style>