<script setup lang="ts">
import { useAppStore } from '@/store/modules/app';
import { ArticleApi, ArticleVO, ArticleCatVO} from '@/api/vr/article'


/** 文章详情 表单 */
defineOptions({ name: 'ArticleForm' })

const appStore = useAppStore()
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  title: undefined as string | undefined,
  catId: undefined as number | undefined,
  keywords: undefined as string | undefined,
  content: undefined as string | undefined,
  sort: undefined as number | undefined,
  thumb: undefined as string | undefined,
})
const formRules = reactive({
  title: [{ required: true, message: '文章标题不能为空', trigger: 'blur' }],
  catId: [{ required: true, message: '分类ID不能为空', trigger: 'change' }],
  keywords: [{ required: true, message: '关键词不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '正文不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
// 添加分类列表数据
const catList = ref([] as ArticleCatVO[])

// 获取分类列表
const getCatList = async () => {
  try {
    catList.value = await ArticleApi.getArticleCatList()
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

/** 处理富文本中的图片链接 */
const handleRichTextImages = (content: string | undefined) => {
  if (!content) return content
  
  // 使用正则表达式匹配 img 标签的 src 属性
  return content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, src) => {
    if (!src.startsWith('http')) {
      return match.replace(src, appStore.cdnUrl + src)
    }
    return match
  })
}
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await getCatList()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ArticleApi.getArticle(id)
      // 处理富文本中的图片链接
      formData.value.content = handleRichTextImages(formData.value.content)
      // 处理封面图链接
      if (formData.value.thumb && !formData.value.thumb.startsWith('http')) {
        formData.value.thumb = appStore.cdnUrl + formData.value.thumb
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ArticleVO
    if (data.thumb && data.thumb.startsWith(appStore.cdnUrl)) {
      data.thumb = data.thumb.replace(appStore.cdnUrl, '')
    }
    if (formType.value === 'create') {
      await ArticleApi.createArticle(data)
      message.success(t('common.createSuccess'))
    } else {
      await ArticleApi.updateArticle(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    title: undefined,
    catId: undefined,
    keywords: undefined,
    content: undefined,
    sort: undefined,
    thumb: undefined,
  }
  formRef.value?.resetFields()
}
</script>

<template>
  <div v-if="dialogVisible" class="content-modal">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="文章标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入文章标题" />
      </el-form-item>
      
      <el-form-item label="封面图" prop="thumb">
      <UploadImg v-model="formData.thumb" height="200px" width="200px" :preview="true" upload-path="data/article/new"/>
    </el-form-item>
      <el-form-item label="分类" prop="catId">
        <el-select v-model="formData.catId" placeholder="请选择分类" class="!w-240px">
          <el-option
            v-for="item in catList"
            :key="item.id"
            :label="item.catName" 
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="关键词" prop="keywords">
        <el-input v-model="formData.keywords" placeholder="请输入关键词" />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <Editor v-model="formData.content" height="450px" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          :max="999"
          :min="1"
          size="small"
          v-model="formData.sort"
          placeholder="请输入排序"
        />
      </el-form-item>
      <div class="footer">
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
    </el-form>
  </div>
</template>


<style scoped>
.content-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 100;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
}
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>