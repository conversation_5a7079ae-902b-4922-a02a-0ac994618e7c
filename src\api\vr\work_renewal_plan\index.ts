import request from '@/config/axios'

// 续期套餐 VO
export interface WorkRenewalPlanVO {
  price: number // 价格
  days: number // 续期天数
}

// 续期套餐 API
export const WorkRenewalPlanApi = {
  // 查询续期套餐分页
  getWorkRenewalPlanPage: async (params: any) => {
    return await request.get({ url: `/vr/work-renewal-plan/page`, params })
  },

  // 查询续期套餐详情
  getWorkRenewalPlan: async (id: number) => {
    return await request.get({ url: `/vr/work-renewal-plan/get?id=` + id })
  },

  // 新增续期套餐
  createWorkRenewalPlan: async (data: WorkRenewalPlanVO) => {
    return await request.post({ url: `/vr/work-renewal-plan/create`, data })
  },

  // 修改续期套餐
  updateWorkRenewalPlan: async (data: WorkRenewalPlanVO) => {
    return await request.put({ url: `/vr/work-renewal-plan/update`, data })
  },

  // 删除续期套餐
  deleteWorkRenewalPlan: async (id: number) => {
    return await request.delete({ url: `/vr/work-renewal-plan/delete?id=` + id })
  },

  // 导出续期套餐 Excel
  exportWorkRenewalPlan: async (params) => {
    return await request.download({ url: `/vr/work-renewal-plan/export-excel`, params })
  },
}
