import request from '@/config/axios'

// 系统默认图片素材 VO
export interface DefMediaResVO {
  pkDefmediaMain: number
  absolutelocation: string // 素材绝对路径
  title: string // 素材名称
  type: string // 0 静态图标 1 动态程序循环图标 2 其他资源用的图片，例如添加电话等
}

// 系统默认图片素材 API
export const DefMediaResApi = {
  // 查询系统默认图片素材分页
  getDefMediaResPage: async (params: any) => {
    return await request.get({ url: `/vr/def-media-res/page`, params })
  },

  // 查询系统默认图片素材详情
  getDefMediaRes: async (id: number) => {
    return await request.get({ url: `/vr/def-media-res/get?id=` + id })
  },

  // 新增系统默认图片素材
  createDefMediaRes: async (data: DefMediaResVO) => {
    return await request.post({ url: `/vr/def-media-res/create`, data })
  },

  // 修改系统默认图片素材
  updateDefMediaRes: async (data: DefMediaResVO) => {
    return await request.put({ url: `/vr/def-media-res/update`, data })
  },

  // 删除系统默认图片素材
  deleteDefMediaRes: async (id: number) => {
    return await request.delete({ url: `/vr/def-media-res/delete?id=` + id })
  },

  // 导出系统默认图片素材 Excel
  exportDefMediaRes: async (params) => {
    return await request.download({ url: `/vr/def-media-res/export-excel`, params })
  },
}
