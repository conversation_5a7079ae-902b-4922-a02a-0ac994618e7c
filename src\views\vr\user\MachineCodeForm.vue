<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" width="600px">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="机器码" prop="machineCode" style="margin-bottom: 0">
        <div style="display: flex; gap: 10px;">
          <el-input v-model="formData.code" placeholder="请输入机器码" style="width: 300px;" />
          <el-button type="primary" @click="handleBind" :loading="bindLoading">绑定</el-button>
        </div>
      </el-form-item>
    </el-form>

    
    <!-- 机器码列表 -->
    <el-table :data="machineCodeList" style="width: 100%" v-loading="tableLoading">
      <el-table-column type="index" label="序号" width="80" align="center" />
      <el-table-column prop="code" label="机器码" align="center" />
      <el-table-column prop="activeTime" label="激活时间" align="center" :formatter="dateFormatter" />
    </el-table>
  </Dialog>
</template>

<script setup lang="ts">
import { UserApi } from '@/api/vr/user'
import { dateFormatter } from '@/utils/formatTime'
defineOptions({ name: 'MachineCodeForm' })

const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('机器码管理') // 弹窗的标题
const formLoading = ref(false) // 表单加载
const bindLoading = ref(false) // 绑定按钮loading
const tableLoading = ref(false) // 表格loading

const formData = ref({
  code: ''
})

const formRules = reactive({
  machineCode: [{ required: true, message: '请输入机器码', trigger: 'blur' }]
})

const machineCodeList = ref([]) // 机器码列表

const formRef = ref() // 表单ref

// 获取机器码列表
const getMachineCodeList = async (id: number) => {
  tableLoading.value = true
  try {
    const data = await UserApi.getMachineCodeList(id)
    machineCodeList.value = data
  } finally {
    tableLoading.value = false
  }
}

// 当前用户ID
const userId = ref()

// 打开弹窗
const open = async (id?: number) => {
  formData.value.code = ''
  dialogVisible.value = true
  if (id) {
    userId.value = id
    getMachineCodeList(id)
  }
}

// 绑定机器码
const handleBind = async () => {
  bindLoading.value = true
  try {
    await UserApi.addMachineCode({
      id: userId.value,
      code: formData.value.code
    })
    message.success('绑定成功')
    getMachineCodeList(userId.value)
    formData.value.code = ''
  } finally {
    bindLoading.value = false
  }
}

defineExpose({ open })
</script>
