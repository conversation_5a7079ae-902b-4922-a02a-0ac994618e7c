import request from '@/config/axios'

// 系统套餐 VO
export interface SystemPackageVO {
  id: number // id
  name: string // 套餐名称
  price: string // 价格
  content: string // 套餐详情
  sort: number // 排序规则，越小越靠前
}

// 系统套餐 API
export const SystemPackageApi = {
  // 查询系统套餐分页
  getSystemPackagePage: async (params: any) => {
    return await request.get({ url: `/vr/system-package/page`, params })
  },

  // 查询系统套餐详情
  getSystemPackage: async (id: number) => {
    return await request.get({ url: `/vr/system-package/get?id=` + id })
  },

  // 新增系统套餐
  createSystemPackage: async (data: SystemPackageVO) => {
    return await request.post({ url: `/vr/system-package/create`, data })
  },

  // 修改系统套餐
  updateSystemPackage: async (data: SystemPackageVO) => {
    return await request.put({ url: `/vr/system-package/update`, data })
  },

  // 删除系统套餐
  deleteSystemPackage: async (id: number) => {
    return await request.delete({ url: `/vr/system-package/delete?id=` + id })
  },

  // 导出系统套餐 Excel
  exportSystemPackage: async (params) => {
    return await request.download({ url: `/vr/system-package/export-excel`, params })
  },
}
