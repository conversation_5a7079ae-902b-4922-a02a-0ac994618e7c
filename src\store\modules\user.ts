import { store } from '@/store'
import { defineStore } from 'pinia'
import { getAccessToken, removeToken } from '@/utils/auth'
import { CACHE_KEY, useCache, deleteUserCache } from '@/hooks/web/useCache'
import { getInfo, getTenantByWebsite, loginOut } from '@/api/login'
import { getTenantPage, TenantVO } from '@/api/system/tenant'

const { wsCache } = useCache()

interface UserVO {
  id: number
  avatar: string
  nickname: string
  deptId: number
  tenantId: number
  tenantName: string
  tenantWebsite: string
}

interface TenantInfo {
  id: number
  name: string
}

interface UserInfoVO {
  // USER 缓存
  permissions: string[]
  roles: string[]
  isSetUser: boolean
  user: UserVO
  tenants: TenantInfo[]
  lastTenantFetch: number
  tenantRefreshTimer?: number // 添加定时器引用
}

export const useUserStore = defineStore('admin-user', {
  state: (): UserInfoVO => ({
    permissions: [],
    roles: [],
    isSetUser: false,
    user: {
      id: 0,
      avatar: '',
      nickname: '',
      deptId: 0,
      tenantId: 0,
      tenantName: '',
      tenantWebsite: ''
    },
    tenants: [],
    lastTenantFetch: 0,
    tenantRefreshTimer: undefined
  }),
  getters: {
    getPermissions(): string[] {
      return this.permissions
    },
    getRoles(): string[] {
      return this.roles
    },
    getIsSetUser(): boolean {
      return this.isSetUser
    },
    getUser(): UserVO {
      return this.user
    },
    isSystemTenant(): boolean {
      return this.user.tenantId === 1  // tenantId === 1 表示系统租户
    },
    getAllTenants(): TenantInfo[] {
      return this.tenants
    },
    getTenantName(): (id: number) => string {
      return (id: number) => {
        const tenant = this.tenants.find((t) => t.id === id)
        return tenant?.name || ''
      }
    },
    getTenantWebsite():String{
      return this.user.tenantWebsite
    }
  },
  actions: {
    async setUserInfoAction() {
      if (!getAccessToken()) {
        this.resetState()
        return null
      }
      let userInfo = wsCache.get(CACHE_KEY.USER)
      if (!userInfo) {
        userInfo = await getInfo()
      }
      if(userInfo.user.tenantId === 1){
        await this.fetchTenants()
        this.startTenantRefreshTimer()
      }
      this.permissions = userInfo.permissions
      this.roles = userInfo.roles
      this.user = userInfo.user
      this.isSetUser = true
      wsCache.set(CACHE_KEY.USER, userInfo)
      wsCache.set(CACHE_KEY.ROLE_ROUTERS, userInfo.menus)
    },
    async setUserAvatarAction(avatar: string) {
      const userInfo = wsCache.get(CACHE_KEY.USER)
      // NOTE: 是否需要像`setUserInfoAction`一样判断`userInfo != null`
      this.user.avatar = avatar
      userInfo.user.avatar = avatar
      wsCache.set(CACHE_KEY.USER, userInfo)
    },
    async setUserNicknameAction(nickname: string) {
      const userInfo = wsCache.get(CACHE_KEY.USER)
      // NOTE: 是否需要像`setUserInfoAction`一样判断`userInfo != null`
      this.user.nickname = nickname
      userInfo.user.nickname = nickname
      wsCache.set(CACHE_KEY.USER, userInfo)
    },
    async loginOut() {
      // 清除定时器
      if (this.tenantRefreshTimer) {
        window.clearInterval(this.tenantRefreshTimer)
        this.tenantRefreshTimer = undefined
      }
      await loginOut()
      removeToken()
      deleteUserCache() // 删除用户缓存
      this.resetState()
    },
    async fetchTenants() {
      const currentTime = Date.now()
      // 检查是否需要刷新租户数据（1小时刷新一次）
      if (currentTime - this.lastTenantFetch > 3600000) {
        const { list } = await getTenantPage({
          pageSize: 100,//全量
          status: 0,
        })
        this.tenants = list.map((item: TenantVO) => ({
          id: item.id,
          name: item.name
        }))
        this.lastTenantFetch = currentTime
        wsCache.set(CACHE_KEY.Tenants, this.tenants)
      }
    },
    startTenantRefreshTimer() {
      // 清除可能存在的旧定时器
      if (this.tenantRefreshTimer) {
        window.clearInterval(this.tenantRefreshTimer)
      }
      // 设置新的定时器，每小时刷新一次
      this.tenantRefreshTimer = window.setInterval(() => {
        if (this.isSystemTenant) {
          this.fetchTenants()
        }
      }, 3600000) // 1小时 = 3600000毫秒
    },
    resetState() {
      this.permissions = []
      this.roles = []
      this.isSetUser = false
      this.user = {
        id: 0,
        avatar: '',
        nickname: '',
        deptId: 0,
        tenantId: 0,
        tenantName: '',
        tenantWebsite: ''
      }
      this.tenants = []
      this.lastTenantFetch = 0
      if (this.tenantRefreshTimer) {
        window.clearInterval(this.tenantRefreshTimer)
        this.tenantRefreshTimer = undefined
      }
    }
  }
})

export const useUserStoreWithOut = () => {
  return useUserStore(store)
}
