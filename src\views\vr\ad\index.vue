<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="广告名称" prop="adName">
        <el-input
          v-model="queryParams.adName"
          placeholder="请输入广告名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:ad:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:ad:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="id" align="center" prop="id" />
      <el-table-column label="广告名称" align="center" prop="adName" />
      <el-table-column label="封面" align="center" prop="adContent" >
        <template #default="scope">
          <el-image
            :src="`${appStore.cdnUrl}${scope.row.adContent}?x-oss-process=image/resize,m_fill,h_300,w_300`"
            fit="cover"
            style="width: 30px; height: 30px"
            :preview-src-list="[`${appStore.cdnUrl}${scope.row.adContent}`]"
            :initial-index="0"
            :preview-teleported="true"
            :z-index="3000"
          />
        </template>
      </el-table-column>
      <el-table-column label="广告链接" align="center" prop="adLink" />
      <el-table-column label="广告位置" align="center" prop="position">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.VR_AD_POSITION" :value="scope.row.position" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:ad:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vr:ad:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <AdForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { DICT_TYPE } from '@/utils/dict'
import download from '@/utils/download'
import { AdApi, AdVO } from '@/api/vr/ad'
import AdForm from './AdForm.vue'

/** 站点广告 列表 */
defineOptions({ name: 'Ad' })

const appStore = useAppStore()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const list = ref<AdVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  adLink: undefined,
  adName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await AdApi.getAdPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    await AdApi.deleteAd(id)
    message.success('删除成功')
    getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await AdApi.exportAd(queryParams)
    download.excel(data, '站点广告.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>