import request from '@/config/axios'

// app下载相关 VO
export interface AppDownloadVO {
  title: string // 标题
  releaseTime: string // 发布时间
  entry: string // 发布说明
  url: string // app或者文件url
  type: number // 1-安卓2-ios3-固件4-说明书
}

// app下载相关 API
export const AppDownloadApi = {
  // 查询app下载相关分页
  getAppDownloadPage: async (params: any) => {
    return await request.get({ url: `/vr/app-download/page`, params })
  },

  // 查询app下载相关详情
  getAppDownload: async (id: number) => {
    return await request.get({ url: `/vr/app-download/get?id=` + id })
  },

  // 新增app下载相关
  createAppDownload: async (data: AppDownloadVO) => {
    return await request.post({ url: `/vr/app-download/create`, data })
  },

  // 修改app下载相关
  updateAppDownload: async (data: AppDownloadVO) => {
    return await request.put({ url: `/vr/app-download/update`, data })
  },

  // 删除app下载相关
  deleteAppDownload: async (id: number) => {
    return await request.delete({ url: `/vr/app-download/delete?id=` + id })
  },

  // 导出app下载相关 Excel
  exportAppDownload: async (params) => {
    return await request.download({ url: `/vr/app-download/export-excel`, params })
  },
}
