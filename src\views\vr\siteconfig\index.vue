
<template>
  
  <!-- <div class="site-config">
    <h2>站点配置</h2>
  </div> -->
  
  <div class="form-container" style="width: 800px">
    <el-form :model="formData" label-width="120px">
      <el-form-item label="网站名称" >
        <el-input v-model="formData.siteName" placeholder="请输入网站名称"/>
      </el-form-item>
      
      <el-form-item label="网站logo" prop="logo">
        <UploadImg v-model="formData.logo" height="200px" width="200px" :preview="true" upload-path="public/image"/>
        <div class="upload-ratio-suggestion">建议上传尺寸: 200px * 40px</div>
      </el-form-item>
      
      <el-form-item label="底部logo">
        <UploadImg v-model="formData.bottomLogo" height="200px" width="200px" :preview="true" upload-path="public/image"/>
      </el-form-item>

      <el-form-item label="微信二维码">
        <UploadImg v-model="formData.wxQr" height="200px" width="200px" :preview="true" upload-path="public/image"/>
      </el-form-item>

      <el-form-item label="QQ">
        <el-input v-model="formData.qq" placeholder="请输入QQ号码" />
      </el-form-item>

      <el-form-item label="联系电话">
        <el-input v-model="formData.telephone" placeholder="请输入联系电话" />
      </el-form-item>

      <el-form-item label="关于我们">
        <Editor v-model="formData.about" height="200px" />
      </el-form-item>
      <el-form-item label="联系我们">
        <Editor v-model="formData.contact" height="200px" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="saveContent">保存配置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app'
import { SiteConfigApi, SiteConfigVO } from '@/api/vr/siteconfig'

const formData = ref<SiteConfigVO>({
  siteName: '',
  logo: '',
  bottomLogo: '',
  wxQr: '',
  qq: '',
  telephone: ''
})
  
const appStore = useAppStore()
const message = useMessage()
const imageFields = ['logo', 'bottomLogo', 'wxQr']

const saveContent = async () => {
  const data = formData.value as unknown as SiteConfigVO
  data.about = handleRichTextImages(data.about)
  data.contact = handleRichTextImages(data.contact)
  imageFields.forEach(field => {
    if (data[field]) {
      data[field] = data[field].replace(appStore.cdnUrl, '')
    }
  })
  await SiteConfigApi.updateSiteConfig(data)
  message.success('保存成功')
  imageFields.forEach(field => {
    if (formData.value[field] && !formData.value[field].startsWith('http')) {
      formData.value[field] = appStore.cdnUrl + formData.value[field]
    }
  })
}

/** 处理富文本中的图片链接 */
const handleRichTextImages = (content: string | undefined) => {
  if (!content) return content
  
  // 使用正则表达式匹配 img 标签的 src 属性
  return content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, src) => {
    if (!src.startsWith('http')) {
      return match.replace(src, appStore.cdnUrl + src)
    }
    return match
  })
}

onMounted(async () => {
  const data = await SiteConfigApi.getSiteConfig()
  if (!data) {
    return
  }
  formData.value = data
  imageFields.forEach(field => {
    if (formData.value[field] && !formData.value[field].startsWith('http')) {
      formData.value[field] = appStore.cdnUrl + formData.value[field]
    }
  })
})
</script>

<style scoped>
.form-container {
  padding: 20px;
}
.site-config {
  text-align: center;
  margin-bottom: 20px;
}
.upload-ratio-suggestion {
  margin-left: 10px;
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
