<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="容量(GB)" prop="space">
        <el-input v-model="formData.space" placeholder="请输入容量" />
      </el-form-item>
      <el-form-item label="价格(元/年)" prop="money">
        <el-input v-model="formData.money" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="上下架" prop="state">
        <el-switch
          v-model.number="formData.state"
          :active-value="1"
          :inactive-value="0"
          active-text="上架"
          inactive-text="下架"
        />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入排序" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { SpacePresetApi, SpacePresetVO } from '@/api/vr/spacepreset'

/** 容量预设套餐 表单 */
defineOptions({ name: 'SpacePresetForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  space: undefined,
  money: undefined,
  state: undefined,
  sort: undefined,
})
const formRules = reactive({
  space: [{ required: true, message: '容量不能为空', trigger: 'blur' }],
  money: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  state: [{ required: true, message: '上下架状态不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SpacePresetApi.getSpacePreset(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SpacePresetVO
    if (formType.value === 'create') {
      await SpacePresetApi.createSpacePreset(data)
      message.success(t('common.createSuccess'))
    } else {
      await SpacePresetApi.updateSpacePreset(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    space: undefined,
    money: undefined,
    state: undefined,
    sort: undefined,
  }
  formRef.value?.resetFields()
}
</script>