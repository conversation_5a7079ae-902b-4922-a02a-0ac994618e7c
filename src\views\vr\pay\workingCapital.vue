<template>
  <ContentWrap>
    <div class="text-center mb-20px text-lg">
      微信运营账号至少需要保证有{{ totalAmount }} 元
    </div>

    <el-table :data="tableData" :stripe="true" fit>
      <el-table-column label="类型" prop="type" align="center" />
      <el-table-column label="金额(元)" prop="amount" align="center">
        <template #default="scope">
          {{ scope.row.amount }} 元
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import { WorkingCapitalApi } from '@/api/vr/pay'
import { ref, computed } from 'vue'

const loading = ref(false)
const workingCapital = ref({
  userAmount: 0,
  redpackAmount: 0,
  rewardAmount: 0
})

const tableData = ref([
  {
    type: '红包提现',
    amount: 0
  },
  {
    type: '红包待抽取', 
    amount: 0
  },
  {
    type: '打赏待提现',
    amount: 0
  }
])

const totalAmount = computed(() => {
  return tableData.value.reduce((sum, item) => sum + item.amount, 0)
})

const getWorkingCapital = async () => {
  loading.value = true
  try {
    const data = await WorkingCapitalApi.getWorkingCapital()
    workingCapital.value = data
    // 更新表格数据
    tableData.value[0].amount = data.userAmount || 0
    tableData.value[1].amount = data.redpackAmount || 0  
    tableData.value[2].amount = data.rewardAmount || 0
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getWorkingCapital()
})
</script>
