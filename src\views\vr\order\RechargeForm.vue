<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="用户id" prop="uid">
        <el-input v-model="formData.uid" placeholder="请输入用户id" />
      </el-form-item>
      <el-form-item label="订单号" prop="orderSn">
        <el-input v-model="formData.orderSn" placeholder="请输入订单号" />
      </el-form-item>
      <el-form-item label="支付方式" prop="payMethod">
        <el-input v-model="formData.payMethod" placeholder="请输入支付方式" />
      </el-form-item>
      <el-form-item label="交易金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入交易金额" />
      </el-form-item>
      <el-form-item label="订单状态，0为待支付，1为支付成功" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio value="1">请选择字典生成</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="容量" prop="originAmount">
        <el-input v-model="formData.originAmount" placeholder="请输入容量" />
      </el-form-item>
      <el-form-item label="2会员升级续费|3提现|4存储空间扩容|5现下支付|6红包充值7红包领取|8打赏" prop="type">
        <el-select v-model="formData.type" placeholder="请选择2会员升级续费|3提现|4存储空间扩容|5现下支付|6红包充值7红包领取|8打赏">
          <el-option label="请选择字典生成" value="" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { RechargeApi, RechargeVO } from '@/api/vr/order'

/** 订单 表单 */
defineOptions({ name: 'RechargeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  uid: undefined,
  orderSn: undefined,
  payMethod: undefined,
  amount: undefined,
  status: undefined,
  originAmount: undefined,
  type: undefined,
})
const formRules = reactive({
  uid: [{ required: true, message: '用户id不能为空', trigger: 'blur' }],
  orderSn: [{ required: true, message: '订单号不能为空', trigger: 'blur' }],
  payMethod: [{ required: true, message: '支付方式不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '交易金额不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '订单状态，0为待支付，1为支付成功不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '2会员升级续费|3提现|4存储空间扩容|5现下支付|6红包充值7红包领取|8打赏不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await RechargeApi.getRecharge(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as RechargeVO
    if (formType.value === 'create') {
      await RechargeApi.createRecharge(data)
      message.success(t('common.createSuccess'))
    } else {
      await RechargeApi.updateRecharge(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    uid: undefined,
    orderSn: undefined,
    payMethod: undefined,
    amount: undefined,
    status: undefined,
    originAmount: undefined,
    type: undefined,
  }
  formRef.value?.resetFields()
}
</script>