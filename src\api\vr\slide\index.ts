import request from '@/config/axios'

// 首页轮播图 VO
export interface SlideVO {
  id: number // id
  imgName: string // 图片名称
  imgPath: string // 图片路径
  link: string // 跳转链接
  sortOrder: number // 排序，升序排列
}

// 首页轮播图 API
export const SlideApi = {
  // 查询首页轮播图分页
  getSlidePage: async (params: any) => {
    return await request.get({ url: `/vr/slide/page`, params })
  },

  // 查询首页轮播图详情
  getSlide: async (id: number) => {
    return await request.get({ url: `/vr/slide/get?id=` + id })
  },

  // 新增首页轮播图
  createSlide: async (data: SlideVO) => {
    return await request.post({ url: `/vr/slide/create`, data })
  },

  // 修改首页轮播图
  updateSlide: async (data: SlideVO) => {
    return await request.put({ url: `/vr/slide/update`, data })
  },

  // 删除首页轮播图
  deleteSlide: async (id: number) => {
    return await request.delete({ url: `/vr/slide/delete?id=` + id })
  },

  // 导出首页轮播图 Excel
  exportSlide: async (params) => {
    return await request.download({ url: `/vr/slide/export-excel`, params })
  },
}
