<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="用户id" prop="pkUserMain">
        <el-input v-model="queryParams.pkUserMain" placeholder="请输入用户id" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="time">
        <el-date-picker v-model="queryParams.time" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:object:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:object:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="缩略图" align="center" prop="thumbPath" :fixed="true" :width="200">
        <template #default="scope">
          <el-image
            :src="`${appStore.cdnUrl}/${scope.row.pkUserMain}/object/${scope.row.uuid}/0.jpg?x-oss-process=image/resize,m_fill,h_300,w_300`"
            fit="cover" style="width: 100px; height: 100px"
            :preview-src-list="[`${appStore.cdnUrl}/${scope.row.pkUserMain}/object/${scope.row.uuid}/0.jpg`]"
            :initial-index="0" :preview-teleported="true" :z-index="3000" />
        </template>
      </el-table-column>
      <el-table-column width="400px" label="物体环绕标题" align="center" prop="title">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            openObject(scope.row.uuid)
          }
            ">{{ scope.row.title }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="项目ID" align="center" prop="id" sortable />
      <el-table-column label="会员id" width="100px" align="center" prop="pkUserMain" sortable>
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            filterUserMainId(scope.row.pkUserMain)
          }
            ">{{ scope.row.pkUserMain }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="180px" label="会员昵称" align="center" prop="pkUserMainName">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            filterUserMainId(scope.row.pkUserMain)
          }
            ">{{ scope.row.pkUserMainName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column label="创建时间" align="center" prop="time" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:object:update']"
          >
            编辑
          </el-button> -->
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['vr:object:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ObjectForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { ObjectApi, ObjectVO } from '@/api/vr/object';
import { useAppStore } from '@/store/modules/app';
import { useUserStore } from '@/store/modules/user';
import download from '@/utils/download';
import { dateFormatter } from '@/utils/formatTime';
import ObjectForm from './ObjectForm.vue';

/** 物体环绕 列表 */
defineOptions({ name: 'Object' })

const userStore = useUserStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const appStore = useAppStore()
const loading = ref(true) // 列表的加载中
const list = ref<ObjectVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pkUserMain: undefined,
  time: [],
  tenantId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ObjectApi.getObjectPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ObjectApi.deleteObject(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ObjectApi.exportObject(queryParams)
    download.excel(data, '物体环绕.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}
const filterUserMainId = (pkUserMain) => {
  queryParams.pkUserMain = pkUserMain
  handleQuery()
}
const openObject = (uuid) => {
  window.open(`${appStore.websiteUrl}/obj/${uuid}`)
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>