<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:slide:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:slide:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>
    <!-- 上传尺寸提示 -->
    <el-alert
      title="轮播图尺寸要求: 1920×585"
      type="info"
      :closable="false"
      class="mb-10px"
    />
  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="预览图" align="center" prop="imgPath" >
        <template #default="scope">
          <el-image
            :src="`${appStore.cdnUrl}${scope.row.imgPath}?x-oss-process=image/resize,m_fill,h_300,w_300`"
            fit="cover"
            style="width: 100px; height: 50px"
            :preview-src-list="[`${appStore.cdnUrl}${scope.row.imgPath}`]"
            :initial-index="0"
            :preview-teleported="true"
            :z-index="3000"
          />
        </template>
      </el-table-column>
      <el-table-column label="跳转链接" align="center" prop="link" />
      <el-table-column label="排序，升序排列" align="center" prop="sortOrder" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:slide:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vr:slide:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <SlideForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app';
import download from '@/utils/download'
import { SlideApi, SlideVO } from '@/api/vr/slide'
import SlideForm from './SlideForm.vue'

/** 首页轮播图 列表 */
defineOptions({ name: 'Slide' })

const appStore = useAppStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<SlideVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  imgName: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await SlideApi.getSlidePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SlideApi.deleteSlide(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await SlideApi.exportSlide(queryParams)
    download.excel(data, '首页轮播图.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>