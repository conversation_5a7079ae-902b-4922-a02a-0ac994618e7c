<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="场景简介" prop="profile">
        <UploadFile v-model="formData.profile" />
      </el-form-item>
      <el-form-item label="缩略图" prop="thumbPath">
        <UploadImg v-model="formData.thumbPath" />
      </el-form-item>
      <el-form-item label="是否推荐 0 不推荐 1推荐" prop="recommend">
        <el-select v-model="formData.recommend" placeholder="请选择是否推荐 0 不推荐 1推荐">
          <el-option
            v-for="dict in getBoolDictOptions(DICT_TYPE.INFRA_BOOLEAN_STRING)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { WorksmainApi, WorksmainVO } from '@/api/vr/worksmain'

/** 全景项目作品 表单 */
defineOptions({ name: 'WorksmainForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  pkWorksMain: undefined,
  name: undefined,
  profile: undefined,
  thumbPath: undefined,
  recommend: undefined,
})
const formRules = reactive({
  thumbPath: [{ required: true, message: '缩略图不能为空', trigger: 'blur' }],
  recommend: [{ required: true, message: '是否推荐 0 不推荐 1推荐不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WorksmainApi.getWorksmain(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WorksmainVO
    if (formType.value === 'create') {
      await WorksmainApi.createWorksmain(data)
      message.success(t('common.createSuccess'))
    } else {
      await WorksmainApi.updateWorksmain(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    pkWorksMain: undefined,
    name: undefined,
    profile: undefined,
    thumbPath: undefined,
    recommend: undefined,
  }
  formRef.value?.resetFields()
}
</script>