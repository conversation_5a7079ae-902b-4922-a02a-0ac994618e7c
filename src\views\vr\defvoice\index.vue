<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="素材名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入素材名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:def-voice:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:def-voice:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="素材名称" align="center" prop="title">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.title }}</span>
            <el-icon 
              class="ml-2 cursor-pointer" 
              @click="handleEditTitle(scope.row)"
              v-hasPermi="['vr:def-media-res:update']"
            >
              <Edit />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="播放音频" align="center" min-width="150px" >
        <template #default="scope">
          <audio :src="scope.row.absolutelocation" controls style="width: 100%;"></audio>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:def-voice:update']"
          >
            编辑
          </el-button> -->
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.pkVoice)"
            v-hasPermi="['vr:def-voice:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DefVoiceForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import download from '@/utils/download'
import { DefVoiceApi, DefVoiceVO } from '@/api/vr/defvoice'
import DefVoiceForm from './DefVoiceForm.vue'
import { Edit } from '@element-plus/icons-vue'

/** 系统默认音频素材 列表 */
defineOptions({ name: 'DefVoice' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DefVoiceVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  title: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DefVoiceApi.getDefVoicePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DefVoiceApi.deleteDefVoice(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DefVoiceApi.exportDefVoice(queryParams)
    download.excel(data, '系统默认音频素材.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

const handleEditTitle = async (row: DefVoiceVO) => {
  const { value } = await ElMessageBox.prompt('请输入新的素材名称', '编辑素材名称', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: row.title,
    inputValidator: (value) => {
      if (!value) {
        return '素材名称不能为空'
      }
      return true
    }
  })
  
  console.log(row)
  if (value) {
    try {
      await DefVoiceApi.updateDefVoice({
        pkVoice: row.pkVoice,
        title: value
      } as unknown as DefVoiceVO )
      message.success('更新成功')
      await getList()
    } catch (error) {
      console.error('更新失败:', error)
    }
  }
}
</script>