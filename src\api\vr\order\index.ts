import request from '@/config/axios'

// 订单 VO
export interface RechargeVO {
  settlementState: number // 结算状态 0未结算 1已结算
  id: number // ID
  uid: number // 用户id
  orderSn: string // 订单号
  payMethod: string // 支付方式
  amount: number // 交易金额
  status: number // 订单状态，0为待支付，1为支付成功
  originAmount: number // 容量
  type: number // 2会员升级续费|3提现|4存储空间扩容|5现下支付|6红包充值7红包领取|8打赏
  parentOrderSn: string // 父订单号
}

// 订单 API
export const RechargeApi = {
  // 查询订单分页
  getRechargePage: async (params: any) => {
    return await request.get({ url: `/vr/recharge/page`, params })
  },

  // 查询订单详情
  getRecharge: async (id: number) => {
    return await request.get({ url: `/vr/recharge/get?id=` + id })
  },

  // 新增订单
  createRecharge: async (data: RechargeVO) => {
    return await request.post({ url: `/vr/recharge/create`, data })
  },

  // 修改订单
  updateRecharge: async (data: RechargeVO) => {
    return await request.put({ url: `/vr/recharge/update`, data })
  },

  // 删除订单
  deleteRecharge: async (id: number) => {
    return await request.delete({ url: `/vr/recharge/delete?id=` + id })
  },

  // 导出订单 Excel
  exportRecharge: async (params) => {
    return await request.download({ url: `/vr/recharge/export-excel`, params })
  },

  // 添加结算接口
  settleOrders: (ids: number[]) => {
    return request.post({url: '/vr/recharge/settlement', data: {ids: ids}})
  },

  // 获取续期信息
  getRenewalInfo: async (orderId: number) => {
    return await request.get({ url: `/vr/work-renewal/getOfOrder?orderId=${orderId}` })
  }
}
