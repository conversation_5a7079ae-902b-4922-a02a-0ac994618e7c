<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-alert
        title="提示"
        type="warning"
        description="该功能为平台外部支付的用户升级账号,请务必如实填写用户支付的金额"
        :closable="false"
        show-icon
         />
         <div style="height: 20px;"></div>
      <el-form-item label="用户组" prop="level">
        <el-select v-model="formData.level" clearable placeholder="请选择用户组">
          <el-option
            v-for="item in levelList"
            :key="item.id"
            :label="item.levelName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="会员到期时间" prop="expire">
        <el-date-picker
          v-model="formData.expire"
          type="datetime"
          placeholder="选择日期时间"
          :shortcuts="shortcuts"
          value-format="x"
          />
      </el-form-item>
      <el-form-item label="线下支付金额" prop="amount">
        <el-input v-model="formData.amount" placeholder="请输入线下支付金额" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { UserApi, UpdaeUserLevelVO } from '@/api/vr/user'
import * as UserLevelApi from '@/api/vr/userLevel'

/** 用户 表单 */
defineOptions({ name: 'UserLevelForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(true) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formData = ref({
  id: -1,
  level: 0,
  expire: 0,
  amount: 0,
})

const formRules = reactive({
  level: [{ required: true, message: '用户组不能为空', trigger: 'blur' }],
  expire: [{ required: true, message: '到期时间不能为空', trigger: 'blur' }],
  amount: [{ required: true, message: '线下支付金额不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const levelList = ref([] as UserLevelApi.UserLevelVO[])


/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  resetForm()
  if (id) {
    formLoading.value = true
    try {
      formData.value = await UserApi.getUser(id)
      formData.value.id = id
      formData.value.expire = formData.value?.expire ? formData.value.expire * 1000 : 0
    } finally {
      formLoading.value = false
    }
  }
  // 加载等级列表
  levelList.value = await UserLevelApi.getUserLevelAll()
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as UpdaeUserLevelVO
      await UserApi.updateUserLevel(data)
      message.success(t('common.updateSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: -1,
    level: 0,
    expire: 0,
    amount: 0,
  }
  formRef.value?.resetFields()
}


const shortcuts = [
  {
    text: '一个月',
    value: () => {
      const date = new Date()
      date.setMonth(date.getMonth() + 1); // 当前日期加上1个月
      return date
    },
  },
  {
    text: '一个季度',
    value: () => {
      const date = new Date();
      date.setMonth(date.getMonth() + 3); // 当前日期加上3个月（1个季度）
      return date
    },
  },
  {
    text: '一年',
    value: () => {
      const date = new Date()
      date.setFullYear(date.getFullYear() + 1); // 当前日期加上1年
      return date
    },
  },
  {
    text: '二年',
    value: () => {
      const date = new Date()
      date.setFullYear(date.getFullYear() + 2); // 当前日期加上2年
      return date
    },
  },
  {
    text: '三年',
    value: () => {
      const date = new Date()
      date.setFullYear(date.getFullYear() + 3); // 当前日期加上3年
      return date
    },
  },
  {
    text: '五年',
    value: () => {
      const date = new Date()
      date.setFullYear(date.getFullYear() + 5); // 当前日期加上5年
      return date
    },
  },
]
</script>