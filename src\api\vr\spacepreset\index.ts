import request from '@/config/axios'

// 容量预设套餐 VO
export interface SpacePresetVO {
  id: number // id
  space: number // 容量
  money: number // 钱
  state: number // 0下架 1上架
  sort: number // 排序
}

// 容量预设套餐 API
export const SpacePresetApi = {
  // 查询容量预设套餐分页
  getSpacePresetPage: async (params: any) => {
    return await request.get({ url: `/vr/space-preset/page`, params })
  },

  // 查询容量预设套餐详情
  getSpacePreset: async (id: number) => {
    return await request.get({ url: `/vr/space-preset/get?id=` + id })
  },

  // 新增容量预设套餐
  createSpacePreset: async (data: SpacePresetVO) => {
    return await request.post({ url: `/vr/space-preset/create`, data })
  },

  // 修改容量预设套餐
  updateSpacePreset: async (data: SpacePresetVO) => {
    return await request.put({ url: `/vr/space-preset/update`, data })
  },

  // 删除容量预设套餐
  deleteSpacePreset: async (id: number) => {
    return await request.delete({ url: `/vr/space-preset/delete?id=` + id })
  },

  // 导出容量预设套餐 Excel
  exportSpacePreset: async (params) => {
    return await request.download({ url: `/vr/space-preset/export-excel`, params })
  },

  // 查询容量预设套餐列表
  getSpacePresetList: async () => {
    return await request.get({ url: `/vr/space-preset/getAll` })
  },
}
