

<template>
  <el-alert
    type="warning"
    description="该功能主要用于IOS APP上架前关闭支付使用,关闭后在ios端不会显示支付按钮,关闭或打开后app端需要重新进入才会同步状态"
    :closable="false"
  />
  <div style="height: 20px;"></div>
  <ContentWrap>
    <!-- 支付配置开关 -->
    <div class="mb-20px">
      <el-switch
        v-model="enabled"
        :loading="loading"
        @change="handleChange"
        active-text="开启支付"
        inactive-text="关闭支付"
      />
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { IosPayApi } from '@/api/vr/pay'

const message = useMessage()
const loading = ref(false)
const enabled = ref(false)

const getConfig = async () => {
  loading.value = true
  try {
    const data = await IosPayApi.getIosPay()
    enabled.value = data
  } finally {
    loading.value = false
  }
}

const handleChange = async (value: boolean) => {
  loading.value = true
  try {
    await IosPayApi.setIosPay(value)
    message.success('更新成功')
  } catch (error) {
    enabled.value = !value
    throw error
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  getConfig()
})
</script>