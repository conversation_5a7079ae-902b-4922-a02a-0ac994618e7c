import request from '@/config/axios'

// 系统默认音频素材 VO
export interface DefVoiceVO {
  pkVoice: any
  absolutelocation: string // 绝对路径
  title: string // 素材名称
}

// 系统默认音频素材 API
export const DefVoiceApi = {
  // 查询系统默认音频素材分页
  getDefVoicePage: async (params: any) => {
    return await request.get({ url: `/vr/def-voice/page`, params })
  },

  // 查询系统默认音频素材详情
  getDefVoice: async (id: number) => {
    return await request.get({ url: `/vr/def-voice/get?id=` + id })
  },

  // 新增系统默认音频素材
  createDefVoice: async (data: DefVoiceVO) => {
    return await request.post({ url: `/vr/def-voice/create`, data })
  },

  // 修改系统默认音频素材
  updateDefVoice: async (data: DefVoiceVO) => {
    return await request.put({ url: `/vr/def-voice/update`, data })
  },

  // 删除系统默认音频素材
  deleteDefVoice: async (id: number) => {
    return await request.delete({ url: `/vr/def-voice/delete?id=` + id })
  },

  // 导出系统默认音频素材 Excel
  exportDefVoice: async (params) => {
    return await request.download({ url: `/vr/def-voice/export-excel`, params })
  },
}
