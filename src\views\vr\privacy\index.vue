<template>
  <div class="privacy-policy">
    <h2>隐私政策</h2>
  </div>
  <div class="editor-container">
    <Editor v-model="formData.content" height="600px" />
    
    <div style="text-align: right; margin-top: 10px;">
      <el-button type="primary" @click="saveContent">保存内容</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/store/modules/app';
import { PrivacyApi, PrivacyVO } from '@/api/vr/privacy';
const formData = ref({
  content: undefined as string | undefined,
})
const appStore = useAppStore()
const message = useMessage()

/** 处理富文本中的图片链接 */
const handleRichTextImages = (content: string | undefined) => {
  if (!content) return '' // 如果内容为空，返回空字符串
  
  // 使用正则表达式匹配 img 标签的 src 属性
  return content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, src) => {
    if (!src.startsWith('http')) {
      return match.replace(src, appStore.cdnUrl + src)
    }
    return match
  })
}

const saveContent = async () => {
  const data = formData.value as unknown as PrivacyVO
  data.content = handleRichTextImages(data.content)
  await PrivacyApi.updatePrivacy(data);
  message.success('保存成功')
};

onMounted(async () => {
  formData.value = await PrivacyApi.getPrivacy()
  formData.value.content = handleRichTextImages(formData.value.content)
})
</script>

<style scoped>
.editor-container {
  padding: 20px;
}
.privacy-policy {
  text-align: center;
  margin-bottom: 20px;
}
</style>
