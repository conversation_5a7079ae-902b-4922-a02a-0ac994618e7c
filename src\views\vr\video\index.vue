<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="用户id" prop="pkUserMain">
        <el-input v-model="queryParams.pkUserMain" placeholder="请输入用户id" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="视频名称" prop="vname">
        <el-input v-model="queryParams.vname" placeholder="请输入视频名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="生成时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <!-- <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:video:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:video:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="缩略图" align="center" prop="thumbPath" :fixed="true" :width="100">
        <template #default="scope">
          <el-image
            :src="`${appStore.cdnUrl}/${JSON.parse(scope.row.videos)[0].location}?x-oss-process=video/snapshot,t_0,m_fast,w_200,h_100`"
            fit="cover" style="width: 100px; height: 100px"
            :preview-src-list="[`${appStore.cdnUrl}/${JSON.parse(scope.row.videos)[0].location}?x-oss-process=video/snapshot,t_0,m_fast`]"
            :initial-index="0" :preview-teleported="true" :z-index="3000" />
        </template>
      </el-table-column>
      <el-table-column width="400px" label="视频标题" align="center" prop="vname">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
              showVideoPreview(scope.row.id)
            }
            ">{{ scope.row.vname }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="项目id" width="100px" align="center" prop="id" sortable />
      <el-table-column label="会员id" width="100px" align="center" prop="pkUserMain" sortable>
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
              filterUserMainId(scope.row.pkUserMain)
            }
            ">{{ scope.row.pkUserMain }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="180px" label="会员昵称" align="center" prop="pkUserMainName">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
              filterUserMainId(scope.row.pkUserMain)
            }
            ">{{ scope.row.pkUserMainName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column label="生成时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column width="150px" label="权重" align="center" prop="sort">
        <template #default="scope">
          <el-input-number v-model="scope.row.sort" size="small" :max="999" :controls="false" :min="1"
            @change="(value) => changeRowSort(scope.row, value)" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:video:update']"
          >
            编辑
          </el-button> -->
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['vr:video:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <VideoForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { VideoApi, VideoVO } from '@/api/vr/video'
import VideoForm from './VideoForm.vue'
import { useAppStore } from '@/store/modules/app';
import { useUserStore } from '@/store/modules/user';

/** 全景视频项目 列表 */
defineOptions({ name: 'Video' })
const appStore = useAppStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<VideoVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  pkUserMain: undefined,
  vname: undefined,
  createTime: [],
  tenantId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中
const userStore = useUserStore()

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await VideoApi.getVideoPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await VideoApi.deleteVideo(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await VideoApi.exportVideo(queryParams)
    download.excel(data, '全景视频项目.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

//修改权重
const changeRowSort = async (row, value) => {
  loading.value = true
  try {
    row['sort'] = value
    await VideoApi.updateVideo(row)
    message.success(t('common.updateSuccess'))
  } finally {
    loading.value = false
    getList()
  }
}

const filterUserMainId = (pkUserMain) => {
  queryParams.pkUserMain = pkUserMain
  handleQuery()
}

const showVideoPreview = (id) => {
  window.open(`${appStore.websiteUrl}/video/play.html?vid=${id}`, '_blank')
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>