<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="价格" prop="price">
        <el-input v-model="formData.price" placeholder="请输入价格" />
      </el-form-item>
      <el-form-item label="续期时长" prop="minutes">
        <el-input v-model="formData.minutes" placeholder="请输入续期时长（分钟）" />
      </el-form-item>
      <el-form-item label="适用天数" prop="applyDays">
        <el-input v-model="formData.applyDays" placeholder="请输入适用天数" />
      </el-form-item>
      <label class="mb-2px">适用天数指的是过期超过这个天数时，会适用该套餐</label>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { WorkRenewalPlanApi, WorkRenewalPlanVO } from '@/api/vr/work_renewal_plan'

/** 续期套餐 表单 */
defineOptions({ name: 'WorkRenewalPlanForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  price: undefined,
  minutes: undefined,
  applyDays: undefined,
})
const formRules = reactive({
  price: [{ required: true, message: '价格不能为空', trigger: 'blur' }],
  minutes: [{ required: true, message: '续期时间不能为空', trigger: 'blur' }],
  applyDays: [{ required: true, message: '适用天数不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await WorkRenewalPlanApi.getWorkRenewalPlan(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as WorkRenewalPlanVO
    if (formType.value === 'create') {
      await WorkRenewalPlanApi.createWorkRenewalPlan(data)
      message.success(t('common.createSuccess'))
    } else {
      await WorkRenewalPlanApi.updateWorkRenewalPlan(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    price: undefined,
    minutes: undefined,
    applyDays: undefined,
  }
  formRef.value?.resetFields()
}
</script>