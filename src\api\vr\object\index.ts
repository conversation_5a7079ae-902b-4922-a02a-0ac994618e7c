import request from '@/config/axios'

// 物体环绕 VO
export interface ObjectVO {
}

// 物体环绕 API
export const ObjectApi = {
  // 查询物体环绕分页
  getObjectPage: async (params: any) => {
    return await request.get({ url: `/vr/object/page`, params })
  },

  // 查询物体环绕详情
  getObject: async (id: number) => {
    return await request.get({ url: `/vr/object/get?id=` + id })
  },

  // 新增物体环绕
  createObject: async (data: ObjectVO) => {
    return await request.post({ url: `/vr/object/create`, data })
  },

  // 修改物体环绕
  updateObject: async (data: ObjectVO) => {
    return await request.put({ url: `/vr/object/update`, data })
  },

  // 删除物体环绕
  deleteObject: async (id: number) => {
    return await request.delete({ url: `/vr/object/delete?id=` + id })
  },

  // 导出物体环绕 Excel
  exportObject: async (params) => {
    return await request.download({ url: `/vr/object/export-excel`, params })
  },
}
