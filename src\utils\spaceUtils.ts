export function convertStorageSize(limit: number): string {
  let size: string = "";

  if (limit === 0) {
    size = '0KB';
  } else if (limit < 0.1 * 1024) { // 如果小于0.1KB转化成B
    size = limit.toFixed(2) + "B";
  } else if (limit < 0.1 * 1024 * 1024) { // 如果小于0.1MB转化成KB
    size = (limit / 1024).toFixed(2) + "KB";
  } else if (limit < 0.1 * 1024 * 1024 * 1024) { // 如果小于0.1GB转化成MB
    size = (limit / (1024 * 1024)).toFixed(2) + "MB";
  } else { // 其他转化成GB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + "GB";
  }

  // 去掉无效的零
  const sizestr = size + ""; // 使用const
  const len = sizestr.indexOf("."); // 使用const
  
  if (len !== -1) {
    const dec = sizestr.substr(len + 1, 2); // 使用const
    
    // 如果小数部分为00，去掉小数部分
    if (dec === "00") {
      return sizestr.substring(0, len) + sizestr.substr(len + 3, 2);
    }
  }

  return sizestr;
}