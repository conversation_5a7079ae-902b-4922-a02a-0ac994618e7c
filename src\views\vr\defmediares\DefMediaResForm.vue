<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">

      <el-form-item label="素材名称" prop="title">
        <el-input v-model="formData.title" placeholder="请输入素材名称" />
      </el-form-item>
      <el-form-item label="类别" prop="type">
        <el-select v-model="formData.type" placeholder="请选择类别">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.U_MEDIARES_TYPE)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="素材文件" prop="absolutelocation">
        <!-- <UploadImg v-model="formData.absolutelocation" :action="`${appStore.websiteUrl}/api2/media/upload`"/> -->
        <el-upload :id="uuid" :action="uploadUrl" :multiple="false" :on-error="(error) => { console.log(error) }"
          :show-file-list="false" :accept="'image/*'" :on-success="(response) => {
            console.log(response)
            formData.suffix = `.${response.result.suffix}` 
            formData.absolutelocation = response.result.dir
            if (formData.type !== 1) {
              formData.thumbPath = response.result.dir
            }
          }">
          <template v-if="formData.absolutelocation">
            <img :src="`${appStore.websiteUrl}${formData.absolutelocation}`" class="upload-image" width="100"
              height="100" />
          </template>
          <template v-else>
            <div class="upload-empty">
              <slot name="empty">
                <Icon icon="ep:plus" />
                <span>点击上传图片</span>
              </slot>
            </div>
          </template>
        </el-upload>

      </el-form-item>
      <el-form-item label="素材缩略图" prop="thumbPath" v-if="formData.type == 1">
        <!-- <UploadImg v-model="formData.absolutelocation" :action="`${appStore.websiteUrl}/api2/media/upload`"/> -->
        <el-upload :id="thumbPathUuid" :action="uploadUrl" :multiple="false"
          :on-error="(error) => { console.log(error) }" :show-file-list="false" :accept="'image/*'" :on-success="(response) => {
            console.log(response)
            formData.thumbPath = response.result.dir
          }">
          <template v-if="formData.thumbPath">
            <img :src="`${appStore.websiteUrl}${formData.thumbPath}`" class="upload-image" width="100" height="100" />
          </template>
          <template v-else>
            <div class="upload-empty">
              <slot name="empty">
                <Icon icon="ep:plus" />
                <span>点击上传图片</span>
              </slot>
            </div>
          </template>
        </el-upload>

      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { DefMediaResApi, DefMediaResVO } from '@/api/vr/defmediares'
import { useAppStore } from '@/store/modules/app';
import { generateUUID } from '@/utils';


/** 系统默认图片素材 表单 */
defineOptions({ name: 'DefMediaResForm' })
const appStore = useAppStore()

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  absolutelocation: undefined as string | undefined,
  title: undefined as string | undefined,
  type: undefined as number | undefined,
  thumbPath: undefined as string | undefined,
  suffix: undefined as string | undefined,
})
const formRules = reactive({
  absolutelocation: [{ required: true, message: '素材文件不能为空', trigger: 'blur' }],
  title: [{ required: true, message: '素材名称不能为空', trigger: 'blur' }],
  type: [{ required: true, message: '类别不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref
// 生成组件唯一id
const uuid = ref('id-' + generateUUID())
const thumbPathUuid = ref('id-' + generateUUID())
/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DefMediaResApi.getDefMediaRes(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DefMediaResVO
    if (formType.value === 'create') {
      await DefMediaResApi.createDefMediaRes(data)
      message.success(t('common.createSuccess'))
    } else {
      await DefMediaResApi.updateDefMediaRes(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    absolutelocation: undefined,
    title: undefined,
    type: undefined,
    thumbPath: undefined,
    suffix: undefined,
  }
  formRef.value?.resetFields()
}


const uploadUrl = computed(() => {
  console.log('WEBSITE_URL:', import.meta.env.VITE_IJOERY_WEBSITE_URL)
  return `${appStore.websiteUrl}/api2/media/upload`
})
</script>