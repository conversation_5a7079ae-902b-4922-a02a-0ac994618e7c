import request from '@/config/axios'
import { da } from 'element-plus/es/locale'


export interface UserInfoVO {
  pkUserMain: number // 主鍵
  phone: string // 手机号，登录账号
  nickname: string // 昵称
  password: string // 密码
  limitSon: number // 子账号数量，为0就按分组
  state: number
  level: number
  expire: Date[]
}

// 用户 VO
export interface UserVO {
  pkUserMain: number // 主鍵
  phone: string // 手机号，登录账号
  nickname: string // 昵称
  password: string // 密码
  limitSon: number // 子账号数量，为0就按分组
}

export interface UpdaeUserLevelVO {
  id: number // 主鍵
  level?: number
  expire?: Date
  amount?: number
}

export interface UpdateUserSpaceVO {
  id: number // 主鍵
  space?: number
  money?: number
}

export interface MachineCodeVO {
  id: number // 主鍵
  code: string // 机器码
}

// 用户 API
export const UserApi = {
  // 查询用户分页
  getUserPage: async (params: any) => {
    return await request.get({ url: `/vr/user/page`, params })
  },

  // 查询用户详情
  getUser: async (id: number) => {
    return await request.get({ url: `/vr/user/get?id=` + id })
  },

  // 新增用户
  createUser: async (data: UserVO) => {
    return await request.post({ url: `/vr/user/create`, data })
  },

  // 修改用户
  updateUser: async (data: UserVO) => {
    return await request.put({ url: `/vr/user/update`, data })
  },

  // 删除用户
  deleteUser: async (id: number) => {
    return await request.delete({ url: `/vr/user/delete?id=` + id })
  },

  // 导出用户 Excel
  exportUser: async (params) => {
    return await request.download({ url: `/vr/user/export-excel`, params })
  },

  // 封号
  updateUserState: async (id: number, status: number) => {
    const data = {
      id,
      status
    }
    return request.put({ url: '/vr/user/updateState', data: data })
  },
  // 变更等级
  updateUserLevel: async (data: UpdaeUserLevelVO) => {
    return await request.put({ url: `/vr/user/updateLevel`, data })
  },

  // 变更用户存储空间
  updateUserSpace: async (data: UpdateUserSpaceVO) => {
    return await request.put({ url: `/vr/user/updateSpace`, data })
  },
  // 绑定机器码
  addMachineCode: async (data: MachineCodeVO) => {
    return await request.put({ url: `/vr/user/bindMachineCode`, data })
  },
  // 查询机器码列表
  getMachineCodeList: async (uid: number) => {
    return await request.get({ url: `/vr/machine-code/getMachineCodeByUserId?uid=` + uid })
  },
  // 查询扩容列表
  getUserSpaceList: async (uid: number) => {
    return await request.post({ url: `/vr/user/spaceList` ,data:{id:uid}})
  },
  // 数据转移
  transferUserDealer: async (userId: number, tenantId: number, targetPhone?: string, code?: string) => {
    const data = {
      userId,
      "targetTenantId":tenantId,
      targetPhone,
      code
    }
    return await request.post({ url: `/vr/user/transfer-dealer`, data })
  },
  
  // 发送数据转移验证码
  sendTransferDealerCode: async (userId: number) => {
    return await request.post({ url: `/vr/user/transfer-dealer-send-code`, data: { "id":userId } })
  }
}
