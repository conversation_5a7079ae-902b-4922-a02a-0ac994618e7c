import request from '@/config/axios'

// 问题分组 VO
export interface HelpGroupVO {
  title: string // 标题
  sort: number // 排序
}

// 问题分组 API
export const HelpGroupApi = {
  // 查询问题分组分页
  getHelpGroupPage: async (params: any) => {
    return await request.get({ url: `/vr/help-group/page`, params })
  },

  // 查询问题分组详情
  getHelpGroup: async (id: number) => {
    return await request.get({ url: `/vr/help-group/get?id=` + id })
  },

  // 新增问题分组
  createHelpGroup: async (data: HelpGroupVO) => {
    return await request.post({ url: `/vr/help-group/create`, data })
  },

  // 修改问题分组
  updateHelpGroup: async (data: HelpGroupVO) => {
    return await request.put({ url: `/vr/help-group/update`, data })
  },

  // 删除问题分组
  deleteHelpGroup: async (id: number) => {
    return await request.delete({ url: `/vr/help-group/delete?id=` + id })
  },

  // 导出问题分组 Excel
  exportHelpGroup: async (params) => {
    return await request.download({ url: `/vr/help-group/export-excel`, params })
  },

// ==================== 子表（问题） ====================

  // 获得问题分页
  getHelpPage: async (params) => {
    return await request.get({ url: `/vr/help-group/help/page`, params })
  },
  // 新增问题
  createHelp: async (data) => {
    return await request.post({ url: `/vr/help-group/help/create`, data })
  },

  // 修改问题
  updateHelp: async (data) => {
    return await request.put({ url: `/vr/help-group/help/update`, data })
  },

  // 删除问题
  deleteHelp: async (id: number) => {
    return await request.delete({ url: `/vr/help-group/help/delete?id=` + id })
  },

  // 获得问题
  getHelp: async (id: number) => {
    return await request.get({ url: `/vr/help-group/help/get?id=` + id })
  },
}
