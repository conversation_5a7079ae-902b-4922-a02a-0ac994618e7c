import request from '@/config/axios'

// 站点广告 VO
export interface AdVO {
  id: number // id
  position: number // 广告位置
  adLink: string // 广告链接
  adName: string // 广告名称
  adContent: string | undefined // 广告内容
}

// 站点广告 API
export const AdApi = {
  // 查询站点广告分页
  getAdPage: async (params: any) => {
    return await request.get({ url: `/vr/ad/page`, params })
  },

  // 查询站点广告详情
  getAd: async (id: number) => {
    return await request.get({ url: `/vr/ad/get?id=` + id })
  },

  // 新增站点广告
  createAd: async (data: AdVO) => {
    return await request.post({ url: `/vr/ad/create`, data })
  },

  // 修改站点广告
  updateAd: async (data: AdVO) => {
    return await request.put({ url: `/vr/ad/update`, data })
  },

  // 删除站点广告
  deleteAd: async (id: number) => {
    return await request.delete({ url: `/vr/ad/delete?id=` + id })
  },

  // 导出站点广告 Excel
  exportAd: async (params) => {
    return await request.download({ url: `/vr/ad/export-excel`, params })
  },
}
