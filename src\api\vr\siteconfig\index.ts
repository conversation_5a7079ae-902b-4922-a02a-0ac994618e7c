import request from '@/config/axios'

// 站点配置 VO
export interface SiteConfigVO {
  siteName?: string // 网站名称
  logo?: string // 网站logo
  bottomLogo?: string // 底部logo
  wxQr?: string // 微信二维码
  qq?: string // qq
  telephone?: string // 联系电话
  about?: string // 关于我们
  contact?: string // 联系我们
}

// 站点配置 API
export const SiteConfigApi = {
  // 查询站点配置详情
  getSiteConfig: async () => {
    return await request.get({ url: `/vr/site-config/get`})
  },


  // 修改站点配置
  updateSiteConfig: async (data: SiteConfigVO) => {
    return await request.put({ url: `/vr/site-config/update`, data })
  },
}
