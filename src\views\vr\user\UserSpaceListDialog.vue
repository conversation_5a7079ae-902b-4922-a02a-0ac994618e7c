<template>
  <el-dialog
    v-model="visible"
    title="扩容记录"
    width="800px"
    destroy-on-close
    append-to-body
  >
    <el-table v-loading="loading" :data="list">
      <el-table-column label="扩容空间" align="center" prop="space">
        <template #default="scope">
          {{ convertStorageSize(scope.row.space * 1024) }}
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="lastExpire" :formatter="dateFormatter" />
      <el-table-column label="到期时间" align="center" prop="curExpire" :formatter="dateFormatter" />
      <el-table-column label="是否过期" align="center" prop="curExpire" >
        <template #default="scope">
          <el-tag :type="scope.row.curExpire > Date.now() ? 'success' : 'danger'">
            {{ scope.row.curExpire > Date.now() ? '未过期' : '已过期' }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { convertStorageSize } from '@/utils/spaceUtils'
import { dateFormatter } from '@/utils/formatTime'
import { UserApi } from '@/api/vr/user'
const visible = ref(false)
const loading = ref(false)
const list = ref([])

const open = async (userId: number) => {
  visible.value = true
  loading.value = true
  try {
    // 假设这是获取未过期扩容记录的API
    const data = await UserApi.getUserSpaceList(userId)
    list.value = data
  } catch (error) {
    console.error('获取扩容记录失败:', error)
  } finally {
    loading.value = false
  }
}

defineExpose({
  open
})
</script>