# 本地开发环境：本地启动所有项目（前端、后端、APP）时使用，不依赖外部环境
NODE_ENV=development

VITE_DEV=true

# 请求路径
VITE_BASE_URL='http://localhost:48080'

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持 S3 服务
VITE_UPLOAD_TYPE=client

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=false

# 是否删除console.log
VITE_DROP_CONSOLE=false

# 是否sourcemap
VITE_SOURCEMAP=false

# 打包路径
VITE_BASE_PATH=/

# 商城H5会员端域名
VITE_MALL_H5_DOMAIN='http://localhost:3000'

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false

# 艾卓悦官网，经销商官网在后面拼接经销商id 如：https://www.ijoyery.com/123456
VITE_IJOERY_WEBSITE_URL = http://************:84/