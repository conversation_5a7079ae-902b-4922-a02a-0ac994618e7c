<template>
  <div v-if="dialogVisible" class="content-modal">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="标题" prop="title" style="width: 700px;">
        <el-input v-model="formData.title" placeholder="请输入标题" />
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          :max="999"
          :min="1"
          size="small"
          v-model="formData.sort"
          placeholder="越小越前"
        />
      </el-form-item>
      <el-form-item label="关键词" prop="keywords" style="width: 700px;">
        <el-input v-model="formData.keywords" placeholder="请输入关键词" />
      </el-form-item>
      <el-form-item label="内容" prop="content">
        <Editor v-model="formData.content" height="450px" />
      </el-form-item>
    </el-form>
    <div class="footer">
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </div>
  </div>
</template>

<style scoped>
.content-modal {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 100;
  padding: 20px;
  box-sizing: border-box;
  overflow-y: auto;
}
.footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
<script setup lang="ts">
import { ProductIntroductionApi, ProductIntroductionVO } from '@/api/vr/product_introduction'
import { useAppStore } from '@/store/modules/app';

/** 相机产品介绍 表单 */
defineOptions({ name: 'ProductIntroductionForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  title: undefined as string | undefined,
  keywords: undefined as string | undefined,
  content: undefined as string | undefined,
  sort: undefined as number | undefined,
})
const formRules = reactive({
  title: [{ required: true, message: '标题不能为空', trigger: 'blur' }],
  keywords: [{ required: true, message: '关键词不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '内容不能为空', trigger: 'blur' }],
  sort: [{ required: true, message: '排序规则，越小越靠前不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref

const appStore = useAppStore()

/** 处理富文本中的图片链接 */
const handleRichTextImages = (content: string | undefined) => {
  if (!content) return content
  
  // 使用正则表达式匹配 img 标签的 src 属性
  return content.replace(/<img[^>]*src="([^"]*)"[^>]*>/g, (match, src) => {
    if (!src.startsWith('http')) {
      return match.replace(src, appStore.cdnUrl + src)
    }
    return match
  })
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProductIntroductionApi.getProductIntroduction(id)
      // 处理富文本中的图片链接
      formData.value.content = handleRichTextImages(formData.value.content)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProductIntroductionVO
    if (formType.value === 'create') {
      await ProductIntroductionApi.createProductIntroduction(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProductIntroductionApi.updateProductIntroduction(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    title: undefined,
    keywords: undefined,
    content: undefined,
    sort: undefined,
  }
  formRef.value?.resetFields()
}
</script>