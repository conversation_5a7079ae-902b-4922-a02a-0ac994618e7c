<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="会员id" prop="pkUserMain">
        <el-input v-model="queryParams.pkUserMain" placeholder="请输入会员id" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item v-if="userStore.isSystemTenant" label="经销商" prop="tenantId">
        <el-select v-model="queryParams.tenantId" placeholder="请选择经销商" clearable class="!w-240px">
          <el-option v-for="item in userStore.getAllTenants" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入项目名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="发布时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['vr:worksmain:create']">
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['vr:worksmain:export']">
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true"
      @filter-change="handleFilterChange">
      <el-table-column label="缩略图" align="center" prop="thumbPath">
        <template #default="scope">
          <el-image :src="formatThumbPath(scope.row.thumbPath, scope.row.pkUserMain)" fit="cover"
            style="width: 100px; height: 100px" :preview-src-list="[formatThumbPath(scope.row.thumbPath, scope.row.pkUserMain)]"
            :initial-index="0" :preview-teleported="true" :z-index="3000" />
        </template>
      </el-table-column>
      <el-table-column width="280px" label="项目名称" align="center" prop="name">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            showWorksmainPreview(scope.row.viewUuid)
          }
            ">{{ scope.row.name }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="80px" label="项目id" align="center" prop="pkWorksMain" />
      <el-table-column width="80px" label="会员id" align="center" prop="pkUserMain">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            filterUserMainId(scope.row.pkUserMain)
          }
            ">{{ scope.row.pkUserMain }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="180px" label="会员昵称" align="center" prop="pkUserMainName">
        <template #default="scope">
          <el-button :type="'primary'" text @click="() => {
            filterUserMainId(scope.row.pkUserMain)
          }
            ">{{ scope.row.pkUserMainName }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column width="120px" label="所属经销商" v-if="userStore.isSystemTenant" align="center" prop="tenantName" />
      <el-table-column width="100px" label="允许推荐" :filters="[
        { text: '是', value: 'true' },
        { text: '否', value: 'false' }
      ]" :filter-multiple="false" column-key="userRecommend" align="center" prop="userRecommend">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.userRecommend" />
        </template>
      </el-table-column>
      <el-table-column label="是否推荐" align="center" prop="recommend" :filters="[
        { text: '已推荐', value: '1' },
        { text: '未推荐', value: '0' }
      ]" :filter-multiple="false" column-key="recommend">
        <template #default="scope">
          <el-switch v-model="scope.row.recommend" @change="(value) => changeRecommendState(scope.row, value)" />
        </template>
      </el-table-column>
      <el-table-column width="150px" label="权重" align="center" prop="sort">
        <template #default="scope">
          <el-input-number v-model="scope.row.sort" size="small" :max="999" :controls="false" :min="1"
            @change="(value) => changeRowSort(scope.row, value)" />
        </template>
      </el-table-column>
      <el-table-column label="浏览量" sortable align="center" prop="browsingNum" />
      <el-table-column label="点赞量" sortable align="center" prop="praisedNum" />
      <el-table-column label="发布时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)" v-hasPermi="['vr:worksmain:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['vr:worksmain:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <WorksmainForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { WorksmainApi, WorksmainVO } from '@/api/vr/worksmain'
import WorksmainForm from './WorksmainForm.vue'
import { useAppStore } from '@/store/modules/app'
import { useUserStore } from '@/store/modules/user'

/** 全景项目作品 列表 */
defineOptions({ name: 'Worksmain' })

const appStore = useAppStore()
const userStore = useUserStore()
const cdnUrl = computed(() => {
  return appStore.cdnUrl
})
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<WorksmainVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive<{
  pageNo: number
  pageSize: number
  pkUserMain?: string
  name?: string
  createTime: any[]
  userRecommend?: boolean
  recommend?: number,
  tenantId?: number
}>({
  pageNo: 1,
  pageSize: 10,
  pkUserMain: undefined,
  name: undefined,
  createTime: [],
  userRecommend: undefined,
  recommend: undefined,
  tenantId: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await WorksmainApi.getWorksmainPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

// 处理筛选
const handleFilterChange = (values) => {
  queryParams.userRecommend = convertBoolStrValueToBool(values['userRecommend']?.[0])
  queryParams.recommend = convertBoolStrValueToBool(values['recommend']?.[0])
  handleQuery()
}

const convertBoolStrValueToBool = (value) => {
  if (value === undefined) return undefined
  return JSON.parse(value)
}

const changeRecommendState = async (row, value) => {
  loading.value = true
  try {
    row['recommend'] = value
    await WorksmainApi.updateWorksmain(row)
    message.success(t('common.updateSuccess'))
  } finally {
    loading.value = false
    getList()
  }
}

//修改权重
const changeRowSort = async (row, value) => {
  loading.value = true
  try {
    row['sort'] = value
    await WorksmainApi.updateWorksmain(row)
    message.success(t('common.updateSuccess'))
  } finally {
    loading.value = false
    getList()
  }
}
const filterUserMainId = (pkUserMain) => {
  queryParams.pkUserMain = pkUserMain
  handleQuery()
}

const showWorksmainPreview = (viewUuid) => {
  window.open(`${appStore.websiteUrl}/tour/${viewUuid}`, '_blank')
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await WorksmainApi.deleteWorksmain(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await WorksmainApi.exportWorksmain(queryParams)
    download.excel(data, '全景项目作品.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  // 获取URL参数
  const urlParams = new URLSearchParams(window.location.search)

  // 遍历URL参数并更新queryParams
  for (const [key, value] of urlParams.entries()) {
    if (key in queryParams) {
      queryParams[key] = value
    }
  }

  getList()
})

/** 格式化缩略图路径 */
const formatThumbPath = (path: string, userId: number | string) => {
  if (!path) return ''
  
  if (path.includes('%HostPath%')) {
    path = path.replace('%HostPath%', cdnUrl.value);
    console.log(path)
    return path
  } else if (path.includes('%ScenePath%')) {
    path = path.replace('%ScenePath%', `${cdnUrl.value}/${userId}/works`)
    console.log(path)
    return path
  }
  
  return path
}
</script>
