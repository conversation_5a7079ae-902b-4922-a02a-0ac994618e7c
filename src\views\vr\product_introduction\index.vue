<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item>
        <!-- <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button> -->
        <!-- <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button> -->
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:product-introduction:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 添加相机介绍
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:product-introduction:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <!-- <el-table-column label="主键" align="center" prop="id" /> -->
      <el-table-column label="标题" align="center" prop="title" >
        <template #default="scope">
          <el-button
            :type="'primary'"
            text
            @click="
              () => {
                showProductIntroductionPreview(scope.row.id)
              }
            "
            >{{ scope.row.title }}
          </el-button>
        </template>
      </el-table-column>
      <!-- <el-table-column label="关键词" align="center" prop="keywords" /> -->
      <!-- <el-table-column label="内容" align="center" prop="content" /> -->
      <el-table-column label="排序" align="center" prop="sort" >
        <template #default="scope">
          <el-input-number
            v-model="scope.row.sort"
            size="small"
            :max="999"
            :controls="false"
            :min="1"
            @change="(value) => changeRowSort(scope.row, value)"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="createtime"
        :formatter="dateFormatter"
        width="180px"
      />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:product-introduction:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['vr:product-introduction:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <ProductIntroductionForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { ProductIntroductionApi, ProductIntroductionVO } from '@/api/vr/product_introduction'
import ProductIntroductionForm from './ProductIntroductionForm.vue'
import { useAppStore } from '@/store/modules/app';

/** 相机产品介绍 列表 */
defineOptions({ name: 'ProductIntroduction' })
const appStore = useAppStore()

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<ProductIntroductionVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await ProductIntroductionApi.getProductIntroductionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await ProductIntroductionApi.deleteProductIntroduction(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await ProductIntroductionApi.exportProductIntroduction(queryParams)
    download.excel(data, '相机产品介绍.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

const showProductIntroductionPreview = (id: number) => {
  window.open(`${appStore.websiteUrl}/products/${id}.html`, '_blank')
}

//修改权重
const changeRowSort = async (row, value) => {
  loading.value = true
  try {
    row['sort'] = value
    await ProductIntroductionApi.updateProductIntroduction(row)
    message.success(t('common.updateSuccess'))
  } finally {
    loading.value = false
    getList()
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

</script>