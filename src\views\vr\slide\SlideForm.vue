<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="图片路径" prop="imgPath">
        <UploadImg v-model="formData.imgPath" width="300px" height="100px" />
      </el-form-item>
      <el-form-item label="跳转链接" prop="link">
        <el-input v-model="formData.link" placeholder="请输入跳转链接" />
      </el-form-item>
      <el-form-item label="排序" prop="sortOrder">
        <el-input-number
          :max="999"
          :min="1"
          size="small"
          v-model="formData.sortOrder"
          placeholder="越小越前"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { SlideApi, SlideVO } from '@/api/vr/slide'
import { useAppStore } from '@/store/modules/app';

/** 首页轮播图 表单 */
defineOptions({ name: 'SlideForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  imgName: undefined,
  imgPath: undefined as string | undefined,
  link: undefined as string | undefined,
  sortOrder: undefined as number | undefined,
})
const formRules = reactive({
  imgPath: [{ required: true, message: '图片路径不能为空', trigger: 'blur' }],
  link: [{ required: true, message: '跳转链接不能为空', trigger: 'blur' }],
  sortOrder: [{ required: true, message: '排序，升序排列不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const appStore = useAppStore()

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await SlideApi.getSlide(id)
      // 处理封面图链接
      if (formData.value.imgPath && !formData.value.imgPath.startsWith('http')) {
        formData.value.imgPath = appStore.cdnUrl + formData.value.imgPath
      }
      if (formData.value.imgPath && !formData.value.imgPath.startsWith('http')) {
        formData.value.imgPath = appStore.cdnUrl + formData.value.imgPath
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as SlideVO
    if (data.imgPath && data.imgPath.startsWith(appStore.cdnUrl)) {
      data.imgPath = data.imgPath.replace(appStore.cdnUrl, '')
    }
    if (formType.value === 'create') {
      await SlideApi.createSlide(data)
      message.success(t('common.createSuccess'))
    } else {
      await SlideApi.updateSlide(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    imgName: undefined,
    imgPath: undefined,
    link: undefined,
    sortOrder: undefined,
  }
  formRef.value?.resetFields()
}
</script>