import request from '@/config/axios'

// 机器码 VO
export interface MachineCodeVO {
  code: string // 编码
}

// 机器码 API
export const MachineCodeApi = {
  // 查询机器码分页
  getMachineCodePage: async (params: any) => {
    return await request.get({ url: `/vr/machine-code/page`, params })
  },

  // 查询机器码详情
  getMachineCode: async (id: number) => {
    return await request.get({ url: `/vr/machine-code/get?id=` + id })
  },

  // 新增机器码
  createMachineCode: async (data: MachineCodeVO) => {
    return await request.post({ url: `/vr/machine-code/create`, data })
  },
  
  // 新增机器码
  importMachineCode: async (data: String) => {
    return await request.post({ 
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      url: `/vr/machine-code/import`, 
      data: { "machineCodes": data } 
    })
  },

  // 修改机器码
  updateMachineCode: async (data: MachineCodeVO) => {
    return await request.put({ url: `/vr/machine-code/update`, data })
  },

  // 删除机器码
  deleteMachineCode: async (id: number) => {
    return await request.delete({ url: `/vr/machine-code/delete?id=` + id })
  },

  // 导出机器码 Excel
  exportMachineCode: async (params) => {
    return await request.download({ url: `/vr/machine-code/export-excel`, params })
  },
}
