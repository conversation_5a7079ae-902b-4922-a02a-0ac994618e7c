<template>
  <div class="head-container">
    <el-input v-model="deptName" class="mb-20px" clearable placeholder="请输入部门名称">
      <template #prefix>
        <Icon icon="ep:search" />
      </template>
    </el-input>
  </div>
  <div class="head-container">
    <el-tree
      ref="treeRef"
      :data="deptList"
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
      :props="defaultProps"
      default-expand-all
      highlight-current
      node-key="id"
      @node-click="handleNodeClick"
    />
  </div>
</template>

<script lang="ts" setup>
import { ElTree } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { defaultProps, handleTree } from '@/utils/tree'
import { useUserStore } from '@/store/modules/user'

defineOptions({ name: 'SystemUserDeptTree' })

const userStore = useUserStore()
const deptName = ref('')
const deptList = ref<Tree[]>([])
const treeRef = ref<InstanceType<typeof ElTree>>()

/** 获得部门树 */
const getTree = async () => {
  const res = await DeptApi.getSimpleDeptList()
  deptList.value = []
  
  if (userStore.isSystemTenant) {
    // 系统租户模式：按租户分组
    const tenantGroups = new Map()
    userStore.getAllTenants.forEach(tenant => {
      tenantGroups.set(tenant.id, {
        id: `tenant-${tenant.id}`,
        name: tenant.name,
        children: []
      })
    })
    
    // 按租户分组部门数据
    const deptsByTenant = new Map()
    res.forEach((dept) => {
      if (!deptsByTenant.has(dept.tenantId)) {
        deptsByTenant.set(dept.tenantId, [])
      }
      deptsByTenant.get(dept.tenantId).push(dept)
    })
    
    // 为每个租户处理部门树
    deptsByTenant.forEach((depts, tenantId) => {
      const group = tenantGroups.get(tenantId)
      if (group) {
        // 处理每个租户内部的部门树
        group.children = handleTree(depts)
      }
    })
    
    // 将分组后的数据添加到列表中
    deptList.value = Array.from(tenantGroups.values()).filter(group => group.children.length > 0)
  } else {
    // 非系统租户模式：直接使用树形结构
    deptList.value = handleTree(res)
  }
}

/** 基于名字过滤 */
const filterNode = (name: string, data: Tree) => {
  if (!name) return true
  return data.name.includes(name)
}

/** 处理部门被点击 */
const handleNodeClick = async (row: { [key: string]: any }) => {
  // 如果是租户节点（ID以tenant-开头），则不触发筛选
  // if (!row.id.toString().startsWith('tenant-')) {
    emits('node-click', row)
  // }
}
const emits = defineEmits(['node-click'])

/** 监听deptName */
watch(deptName, (val) => {
  treeRef.value!.filter(val)
})

/** 初始化 */
onMounted(async () => {
  await getTree()
})

/** 重置当前节点 */
const resetCurrentNode = () => {
  // 重置选中的节点
  treeRef.value?.setCurrentKey(undefined)
}

/** 暴露方法 */
defineExpose({ resetCurrentNode })
</script>
