<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="素材名称" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入素材名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="类别" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择类别"
          clearable
          class="!w-240px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.U_MEDIARES_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['vr:def-media-res:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 上传图片素材
        </el-button>
        <!-- <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['vr:def-media-res:export']"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button> -->
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true">
      <el-table-column label="缩略图" align="center" prop="thumbPath" >
        <template #default="scope">
          <el-image
            :src="`${appStore.websiteUrl}/${scope.row.thumbPath}?x-oss-process=image/resize,m_fill,h_300,w_300`"
            fit="cover"
            style="width: 50px; height: 50px"
            :preview-src-list="[`${appStore.websiteUrl}/${scope.row.thumbPath}`]"
            :initial-index="0"
            :preview-teleported="true"
            :z-index="3000"
          />
        </template>
      </el-table-column>
      <el-table-column label="素材名称" align="center" prop="title">
        <template #default="scope">
          <div class="flex items-center justify-center">
            <span>{{ scope.row.title }}</span>
            <el-icon 
              class="ml-2 cursor-pointer" 
              @click="handleEditTitle(scope.row)"
              v-hasPermi="['vr:def-media-res:update']"
            >
              <Edit />
            </el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="类别" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.U_MEDIARES_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <!-- <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['vr:def-media-res:update']"
          >
            编辑
          </el-button> -->
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.pkDefmediaMain)"
            v-hasPermi="['vr:def-media-res:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DefMediaResForm ref="formRef" @success="getList" />
</template>
<script setup lang="ts">
import { useAppStore } from '@/store/modules/app';
import DefMediaResForm from './DefMediaResForm.vue'
import { Edit } from '@element-plus/icons-vue'
import { DICT_TYPE } from '@/utils/dict'
import { DefMediaResApi, DefMediaResVO } from '@/api/vr/defmediares';
import download from '@/utils/download';
import { getStrDictOptions } from '@/utils/dict'

/** 系统默认图片素材 列表 */
defineOptions({ name: 'DefMediaRes' })
const appStore = useAppStore()
const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DefMediaResVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  absolutelocation: undefined,
  title: undefined,
  type: undefined,
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DefMediaResApi.getDefMediaResPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DefMediaResApi.deleteDefMediaRes(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await DefMediaResApi.exportDefMediaRes(queryParams)
    download.excel(data, '系统默认图片素材.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

const handleEditTitle = async (row: DefMediaResVO) => {
  const { value } = await ElMessageBox.prompt('请输入新的素材名称', '编辑素材名称', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: row.title,
    inputValidator: (value) => {
      if (!value) {
        return '素材名称不能为空'
      }
      return true
    }
  })
  
  if (value) {
    try {
      await DefMediaResApi.updateDefMediaRes({
        pkDefmediaMain: row.pkDefmediaMain,
        title: value
      } as DefMediaResVO )
      message.success('更新成功')
      await getList()
    } catch (error) {
      console.error('更新失败:', error)
    }
  }
}
</script>